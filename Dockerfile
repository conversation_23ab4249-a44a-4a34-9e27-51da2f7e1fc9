# Build stage
FROM golang:1.23-alpine AS builder

# Set working directory
WORKDIR /app

# Install git and ca-certificates
RUN apk add --no-cache git ca-certificates

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o waf ./cmd/waf

# Final stage
FROM alpine:latest

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates

# Create non-root user
RUN addgroup -g 1001 -S waf && \
    adduser -u 1001 -S waf -G waf

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/waf .

# Copy configuration file
COPY --from=builder /app/config.yml .

# Create log directory
RUN mkdir -p /var/log && chown waf:waf /var/log

# Change ownership of app directory
RUN chown -R waf:waf /app

# Switch to non-root user
USER waf

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Run the application
CMD ["./waf"]
