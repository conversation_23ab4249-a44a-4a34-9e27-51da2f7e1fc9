# WAF Makefile

# Variables
BINARY_NAME=waf
DOCKER_IMAGE=waf-app
DOCKER_TAG=latest
GO_VERSION=1.21

# Default target
.PHONY: help
help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Build targets
.PHONY: build
build: ## Build the WAF binary
	go build -o bin/$(BINARY_NAME) ./cmd/waf

.PHONY: build-linux
build-linux: ## Build the WAF binary for Linux
	GOOS=linux GOARCH=amd64 go build -o bin/$(BINARY_NAME)-linux ./cmd/waf

.PHONY: clean
clean: ## Clean build artifacts
	rm -rf bin/
	go clean

# Development targets
.PHONY: run
run: ## Run the WAF locally
	go run ./cmd/waf

.PHONY: dev
dev: ## Run in development mode with hot reload
	air -c .air.toml

.PHONY: fmt
fmt: ## Format Go code
	go fmt ./...

.PHONY: vet
vet: ## Run go vet
	go vet ./...

.PHONY: lint
lint: ## Run golangci-lint
	golangci-lint run

# Test targets
.PHONY: test
test: ## Run tests
	go test -v ./...

.PHONY: test-coverage
test-coverage: ## Run tests with coverage
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

.PHONY: test-race
test-race: ## Run tests with race detection
	go test -race -v ./...

# Docker targets
.PHONY: docker-build
docker-build: ## Build Docker image
	docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .

.PHONY: docker-run
docker-run: ## Run Docker container
	docker run -p 8080:8080 $(DOCKER_IMAGE):$(DOCKER_TAG)

.PHONY: docker-compose-up
docker-compose-up: ## Start all services with docker-compose
	docker-compose up -d

.PHONY: docker-compose-down
docker-compose-down: ## Stop all services with docker-compose
	docker-compose down

.PHONY: docker-compose-logs
docker-compose-logs: ## Show docker-compose logs
	docker-compose logs -f

# Database targets
.PHONY: db-up
db-up: ## Start PostgreSQL database
	docker-compose up -d postgres

.PHONY: db-down
db-down: ## Stop PostgreSQL database
	docker-compose down postgres

.PHONY: db-migrate
db-migrate: ## Run database migrations
	go run ./cmd/migrate

.PHONY: db-reset
db-reset: ## Reset database (drop and recreate)
	docker-compose down postgres
	docker volume rm waf-fav_postgres_data || true
	docker-compose up -d postgres

# Dependencies
.PHONY: deps
deps: ## Download dependencies
	go mod download
	go mod tidy

.PHONY: deps-update
deps-update: ## Update dependencies
	go get -u ./...
	go mod tidy

# Security
.PHONY: security-check
security-check: ## Run security checks
	gosec ./...

# All-in-one targets
.PHONY: check
check: fmt vet lint test ## Run all checks

.PHONY: install-tools
install-tools: ## Install development tools
	go install github.com/cosmtrek/air@latest
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
