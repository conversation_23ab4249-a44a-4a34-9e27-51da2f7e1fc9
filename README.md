# WAF-FAV: Policy-Based Web Application Firewall

A high-performance, policy-based Web Application Firewall (WAF) built with Go that operates as a reverse proxy to protect web applications from various security threats.

## 🛡️ Features

### Core Functionality
- **Reverse Proxy Architecture**: Seamlessly integrates between clients and upstream servers
- **Dual Operation Modes**:
  - **Block Mode**: Actively blocks malicious requests
  - **Log Mode**: Logs threats without blocking (monitoring mode)
- **Policy-Based Security**: Flexible rule engine with customizable security policies
- **Real-time Threat Detection**: Advanced pattern matching for various attack vectors

### Security Protection
- **SQL Injection Protection**: Detects and blocks SQL injection attempts
- **Cross-Site Scripting (XSS) Prevention**: Identifies XSS attack patterns
- **Path Traversal Protection**: Prevents directory traversal attacks
- **Command Injection Detection**: Blocks command injection attempts
- **Rate Limiting**: Configurable request rate limiting per IP
- **IP Blocking**: Temporary and permanent IP blocking capabilities
- **File Inclusion Protection**: Detects LFI/RFI attempts

### Monitoring & Analytics
- **Comprehensive Logging**: Detailed request and security event logging
- **PostgreSQL Integration**: Persistent storage for logs and analytics
- **Real-time Metrics**: Performance and security metrics collection
- **Statistics Dashboard**: Request statistics and threat analysis
- **Security Event Tracking**: Detailed security incident reporting

### Management & Configuration
- **YAML Configuration**: Easy-to-manage configuration files
- **REST API**: Management API for policies and monitoring
- **Hot Reload**: Dynamic policy updates without restart
- **Health Checks**: Built-in health monitoring endpoints
- **Docker Support**: Containerized deployment with Docker Compose

## 🏗️ Architecture

```
Client Request → WAF (Policy Engine) → Upstream Server
                     ↓
              PostgreSQL Database
                (Logs & Policies)
```

### Components
- **WAF Engine**: Core security processing engine
- **Policy Engine**: Rule evaluation and threat detection
- **Reverse Proxy**: HTTP request/response handling
- **Database Layer**: PostgreSQL integration for persistence
- **Monitoring System**: Metrics collection and reporting
- **Management API**: RESTful API for administration

## 🚀 Quick Start

### Prerequisites
- Go 1.21 or higher
- PostgreSQL 12 or higher
- Docker and Docker Compose (optional)

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/your-username/waf-fav.git
cd waf-fav
```

2. **Install dependencies**
```bash
go mod download
```

3. **Configure the application**
```bash
cp config.yml.example config.yml
# Edit config.yml with your settings
```

4. **Set up the database**
```bash
# Start PostgreSQL (if using Docker)
docker-compose up -d postgres

# Run migrations
make db-migrate
```

5. **Build and run**
```bash
# Build the application
make build

# Run the WAF
./bin/waf
```

### Docker Deployment

1. **Using Docker Compose**
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f waf
```

2. **Build custom image**
```bash
# Build Docker image
make docker-build

# Run container
make docker-run
```

## ⚙️ Configuration

### Basic Configuration (config.yml)

```yaml
# Server Configuration
server:
  port: 8080
  host: "0.0.0.0"
  read_timeout: 30s
  write_timeout: 30s

# WAF Mode: "block" or "log"
mode: "log"

# Upstream server
upstream:
  host: "localhost"
  port: 3000
  scheme: "http"

# Database
database:
  host: "localhost"
  port: 5432
  name: "waf_db"
  user: "waf_user"
  password: "waf_password"

# Security Policies
policies:
  sql_injection: true
  xss: true
  path_traversal: true
  command_injection: true
  rate_limiting: true
  ip_blocking: true

  rate_limit:
    requests_per_minute: 100
    burst_size: 20
```

### Environment Variables

Key environment variables for configuration override:

```bash
WAF_MODE=block                    # Operation mode
WAF_SERVER_PORT=8080             # Server port
WAF_DB_HOST=localhost            # Database host
WAF_DB_PASSWORD=secret           # Database password
WAF_UPSTREAM_HOST=backend        # Upstream server host
```

## 📊 Management API

### Health Check
```bash
GET /health
```

### Statistics
```bash
GET /api/statistics?days=7
```

### Policy Management
```bash
# List policies
GET /api/policies

# Create policy
POST /api/policies
{
  "name": "Custom SQL Injection Rule",
  "category": "sql_injection",
  "pattern": "(?i)(union\\s+select)",
  "action": "block",
  "enabled": true,
  "priority": 90
}

# Update policy
PUT /api/policies/{id}

# Delete policy
DELETE /api/policies/{id}
```

### Blocked IPs
```bash
# List blocked IPs
GET /api/blocked-ips
```

### Metrics
```bash
# Get real-time metrics
GET /api/metrics
```

## 🧪 Testing

### Run Tests
```bash
# Run all tests
make test

# Run tests with coverage
make test-coverage

# Run race condition tests
make test-race
```

### Test WAF Protection

1. **Start the test environment**
```bash
docker-compose up -d
```

2. **Test legitimate request**
```bash
curl http://localhost:8080/api/test
```

3. **Test SQL injection (should be blocked in block mode)**
```bash
curl "http://localhost:8080/api/vulnerable?id=1' OR '1'='1"
```

4. **Test XSS (should be detected)**
```bash
curl "http://localhost:8080/api/vulnerable?name=<script>alert('xss')</script>"
```

## 📈 Monitoring

### Metrics Available
- Total requests processed
- Blocked vs allowed requests
- Response times
- Top threat types
- Top attacking IPs
- Policy match statistics
- Rate limiting events

### Log Analysis
```bash
# View WAF logs
docker-compose logs -f waf

# Query database for security events
psql -h localhost -U waf_user -d waf_db -c "SELECT * FROM security_events ORDER BY created_at DESC LIMIT 10;"
```

## 🔧 Development

### Project Structure
```
waf-fav/
├── cmd/waf/              # Application entry point
├── internal/
│   ├── config/           # Configuration management
│   ├── database/         # Database layer
│   ├── logging/          # Logging system
│   ├── monitoring/       # Metrics collection
│   ├── policy/           # Policy engine
│   ├── proxy/            # Reverse proxy
│   ├── server/           # HTTP server
│   └── waf/              # WAF core engine
├── migrations/           # Database migrations
├── test/                 # Test files and fixtures
├── config.yml            # Configuration file
├── docker-compose.yml    # Docker composition
├── Dockerfile            # Container definition
└── Makefile              # Build automation
```

### Adding Custom Policies

1. **Database approach** (recommended):
```sql
INSERT INTO policies (name, category, pattern, action, enabled, priority, description)
VALUES ('Custom Rule', 'custom', '(?i)malicious_pattern', 'block', true, 80, 'Custom security rule');
```

2. **API approach**:
```bash
curl -X POST http://localhost:8080/api/policies \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Custom Rule",
    "category": "custom",
    "pattern": "(?i)malicious_pattern",
    "action": "block",
    "enabled": true,
    "priority": 80,
    "description": "Custom security rule"
  }'
```

### Building from Source

```bash
# Install development tools
make install-tools

# Format code
make fmt

# Run linting
make lint

# Run security checks
make security-check

# Build for different platforms
make build-linux
```

## 🐳 Production Deployment

### Docker Production Setup

1. **Create production configuration**
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  waf:
    image: waf-fav:latest
    environment:
      - WAF_MODE=block
      - WAF_DB_PASSWORD=${DB_PASSWORD}
    volumes:
      - ./config.prod.yml:/app/config.yml:ro
    restart: unless-stopped
```

2. **Deploy with secrets**
```bash
export DB_PASSWORD="your-secure-password"
docker-compose -f docker-compose.prod.yml up -d
```

### Performance Tuning

- **Database connections**: Adjust `max_open_conns` and `max_idle_conns`
- **Rate limiting**: Configure appropriate limits for your traffic
- **Policy optimization**: Prioritize frequently matched policies
- **Logging level**: Use "warn" or "error" in production for better performance

## 🔒 Security Considerations

- **Database security**: Use strong passwords and SSL connections
- **Network security**: Deploy behind a load balancer with SSL termination
- **Access control**: Restrict management API access
- **Regular updates**: Keep policies and patterns updated
- **Monitoring**: Set up alerts for security events

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/your-username/waf-fav/issues)
- **Documentation**: [Wiki](https://github.com/your-username/waf-fav/wiki)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/waf-fav/discussions)

## 🙏 Acknowledgments

- Go community for excellent libraries
- OWASP for security guidelines
- PostgreSQL team for robust database system
- Docker for containerization platform

---

**WAF-FAV** - Protecting web applications with intelligent policy-based security.