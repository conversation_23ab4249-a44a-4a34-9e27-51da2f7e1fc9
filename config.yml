# WAF Configuration File

# Server Configuration
server:
  port: 8080
  host: "0.0.0.0"
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 60s

# WAF Mode: "block" or "log"
# block: Blocks malicious requests
# log: Only logs malicious requests without blocking
mode: "log"

# Upstream server configuration
upstream:
  host: "localhost"
  port: 3000
  scheme: "http"
  timeout: 30s

# Database Configuration
database:
  host: "localhost"
  port: 5432
  name: "waf_db"
  user: "waf_user"
  password: "waf_password"
  ssl_mode: "disable"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300s

# Policy Configuration
policies:
  # Enable/disable specific policy categories
  sql_injection: true
  xss: true
  path_traversal: true
  command_injection: true
  rate_limiting: true
  ip_blocking: true
  
  # Rate limiting configuration
  rate_limit:
    requests_per_minute: 100
    burst_size: 20
    
  # IP blocking configuration
  blocked_ips:
    - "*************"
    - "*********"
  
  # Allowed IPs (whitelist)
  allowed_ips:
    - "127.0.0.1"
    - "::1"

# Logging Configuration
logging:
  level: "info"  # debug, info, warn, error
  format: "json" # json, text
  output: "stdout" # stdout, file
  file_path: "/var/log/waf.log"
  
# Security Headers
security_headers:
  enable: true
  headers:
    X-Frame-Options: "DENY"
    X-Content-Type-Options: "nosniff"
    X-XSS-Protection: "1; mode=block"
    Strict-Transport-Security: "max-age=31536000; includeSubDomains"
    Content-Security-Policy: "default-src 'self'"
