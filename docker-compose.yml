version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: waf-postgres
    environment:
      POSTGRES_DB: waf_db
      POSTGRES_USER: waf_user
      POSTGRES_PASSWORD: waf_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    networks:
      - waf-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U waf_user -d waf_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # WAF Application
  waf:
    build: .
    container_name: waf-app
    ports:
      - "8080:8080"
    volumes:
      - ./config.yml:/app/config.yml:ro
      - waf_logs:/var/log
    environment:
      - WAF_CONFIG_PATH=/app/config.yml
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - waf-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Example upstream application (for testing)
  upstream-app:
    image: nginx:alpine
    container_name: waf-upstream
    ports:
      - "3000:80"
    volumes:
      - ./test/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./test/html:/usr/share/nginx/html:ro
    networks:
      - waf-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  waf_logs:
    driver: local

networks:
  waf-network:
    driver: bridge
