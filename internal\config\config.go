package config

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

// Config represents the main configuration structure
type Config struct {
	Server          ServerConfig          `yaml:"server"`
	Mode            string                `yaml:"mode"`
	Upstream        UpstreamConfig        `yaml:"upstream"`
	Database        DatabaseConfig        `yaml:"database"`
	Policies        PoliciesConfig        `yaml:"policies"`
	Logging         LoggingConfig         `yaml:"logging"`
	SecurityHeaders SecurityHeadersConfig `yaml:"security_headers"`
}

// ServerConfig represents server configuration
type ServerConfig struct {
	Port         int           `yaml:"port"`
	Host         string        `yaml:"host"`
	ReadTimeout  time.Duration `yaml:"read_timeout"`
	WriteTimeout time.Duration `yaml:"write_timeout"`
	IdleTimeout  time.Duration `yaml:"idle_timeout"`
}

// UpstreamConfig represents upstream server configuration
type UpstreamConfig struct {
	Host    string        `yaml:"host"`
	Port    int           `yaml:"port"`
	Scheme  string        `yaml:"scheme"`
	Timeout time.Duration `yaml:"timeout"`
}

// DatabaseConfig represents database configuration
type DatabaseConfig struct {
	Host            string        `yaml:"host"`
	Port            int           `yaml:"port"`
	Name            string        `yaml:"name"`
	User            string        `yaml:"user"`
	Password        string        `yaml:"password"`
	SSLMode         string        `yaml:"ssl_mode"`
	MaxOpenConns    int           `yaml:"max_open_conns"`
	MaxIdleConns    int           `yaml:"max_idle_conns"`
	ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
}

// PoliciesConfig represents policies configuration
type PoliciesConfig struct {
	SQLInjection     bool              `yaml:"sql_injection"`
	XSS              bool              `yaml:"xss"`
	PathTraversal    bool              `yaml:"path_traversal"`
	CommandInjection bool              `yaml:"command_injection"`
	RateLimiting     bool              `yaml:"rate_limiting"`
	IPBlocking       bool              `yaml:"ip_blocking"`
	RateLimit        RateLimitConfig   `yaml:"rate_limit"`
	BlockedIPs       []string          `yaml:"blocked_ips"`
	AllowedIPs       []string          `yaml:"allowed_ips"`
}

// RateLimitConfig represents rate limiting configuration
type RateLimitConfig struct {
	RequestsPerMinute int `yaml:"requests_per_minute"`
	BurstSize         int `yaml:"burst_size"`
}

// LoggingConfig represents logging configuration
type LoggingConfig struct {
	Level    string `yaml:"level"`
	Format   string `yaml:"format"`
	Output   string `yaml:"output"`
	FilePath string `yaml:"file_path"`
}

// SecurityHeadersConfig represents security headers configuration
type SecurityHeadersConfig struct {
	Enable  bool              `yaml:"enable"`
	Headers map[string]string `yaml:"headers"`
}

// Load loads configuration from a YAML file
func Load(filename string) (*Config, error) {
	// Check if file exists
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return nil, fmt.Errorf("configuration file %s does not exist", filename)
	}

	// Read file
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read configuration file: %w", err)
	}

	// Parse YAML
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse configuration file: %w", err)
	}

	// Validate configuration
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	// Apply environment variable overrides
	config.ApplyEnvOverrides()

	return &config, nil
}

// Validate validates the configuration
func (c *Config) Validate() error {
	// Validate mode
	if c.Mode != "block" && c.Mode != "log" {
		return fmt.Errorf("invalid mode: %s (must be 'block' or 'log')", c.Mode)
	}

	// Validate server configuration
	if c.Server.Port <= 0 || c.Server.Port > 65535 {
		return fmt.Errorf("invalid server port: %d", c.Server.Port)
	}

	if c.Server.Host == "" {
		return fmt.Errorf("server host cannot be empty")
	}

	// Validate upstream configuration
	if c.Upstream.Host == "" {
		return fmt.Errorf("upstream host cannot be empty")
	}

	if c.Upstream.Port <= 0 || c.Upstream.Port > 65535 {
		return fmt.Errorf("invalid upstream port: %d", c.Upstream.Port)
	}

	if c.Upstream.Scheme != "http" && c.Upstream.Scheme != "https" {
		return fmt.Errorf("invalid upstream scheme: %s (must be 'http' or 'https')", c.Upstream.Scheme)
	}

	// Validate database configuration
	if c.Database.Host == "" {
		return fmt.Errorf("database host cannot be empty")
	}

	if c.Database.Port <= 0 || c.Database.Port > 65535 {
		return fmt.Errorf("invalid database port: %d", c.Database.Port)
	}

	if c.Database.Name == "" {
		return fmt.Errorf("database name cannot be empty")
	}

	if c.Database.User == "" {
		return fmt.Errorf("database user cannot be empty")
	}

	// Validate logging configuration
	validLogLevels := map[string]bool{
		"debug": true,
		"info":  true,
		"warn":  true,
		"error": true,
	}

	if !validLogLevels[c.Logging.Level] {
		return fmt.Errorf("invalid log level: %s", c.Logging.Level)
	}

	validLogFormats := map[string]bool{
		"json": true,
		"text": true,
	}

	if !validLogFormats[c.Logging.Format] {
		return fmt.Errorf("invalid log format: %s", c.Logging.Format)
	}

	validLogOutputs := map[string]bool{
		"stdout": true,
		"file":   true,
	}

	if !validLogOutputs[c.Logging.Output] {
		return fmt.Errorf("invalid log output: %s", c.Logging.Output)
	}

	if c.Logging.Output == "file" && c.Logging.FilePath == "" {
		return fmt.Errorf("log file path cannot be empty when output is 'file'")
	}

	// Validate rate limiting configuration
	if c.Policies.RateLimiting {
		if c.Policies.RateLimit.RequestsPerMinute <= 0 {
			return fmt.Errorf("requests per minute must be positive")
		}

		if c.Policies.RateLimit.BurstSize <= 0 {
			return fmt.Errorf("burst size must be positive")
		}
	}

	return nil
}

// ApplyEnvOverrides applies environment variable overrides
func (c *Config) ApplyEnvOverrides() {
	// Server overrides
	if port := os.Getenv("WAF_SERVER_PORT"); port != "" {
		// Parse and set port if valid
		// Implementation would include proper parsing
	}

	if host := os.Getenv("WAF_SERVER_HOST"); host != "" {
		c.Server.Host = host
	}

	// Mode override
	if mode := os.Getenv("WAF_MODE"); mode != "" {
		c.Mode = mode
	}

	// Database overrides
	if dbHost := os.Getenv("WAF_DB_HOST"); dbHost != "" {
		c.Database.Host = dbHost
	}

	if dbName := os.Getenv("WAF_DB_NAME"); dbName != "" {
		c.Database.Name = dbName
	}

	if dbUser := os.Getenv("WAF_DB_USER"); dbUser != "" {
		c.Database.User = dbUser
	}

	if dbPassword := os.Getenv("WAF_DB_PASSWORD"); dbPassword != "" {
		c.Database.Password = dbPassword
	}

	// Upstream overrides
	if upstreamHost := os.Getenv("WAF_UPSTREAM_HOST"); upstreamHost != "" {
		c.Upstream.Host = upstreamHost
	}

	if upstreamScheme := os.Getenv("WAF_UPSTREAM_SCHEME"); upstreamScheme != "" {
		c.Upstream.Scheme = upstreamScheme
	}
}

// IsBlockMode returns true if WAF is in block mode
func (c *Config) IsBlockMode() bool {
	return c.Mode == "block"
}

// IsLogMode returns true if WAF is in log mode
func (c *Config) IsLogMode() bool {
	return c.Mode == "log"
}

// GetUpstreamURL returns the full upstream URL
func (c *Config) GetUpstreamURL() string {
	return fmt.Sprintf("%s://%s:%d", c.Upstream.Scheme, c.Upstream.Host, c.Upstream.Port)
}
