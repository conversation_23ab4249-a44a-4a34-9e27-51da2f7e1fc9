package config

import (
	"os"
	"testing"
)

func TestLoad(t *testing.T) {
	// Create a temporary config file
	configContent := `
server:
  port: 8080
  host: "0.0.0.0"
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 60s

mode: "log"

upstream:
  host: "localhost"
  port: 3000
  scheme: "http"
  timeout: 30s

database:
  host: "localhost"
  port: 5432
  name: "waf_db"
  user: "waf_user"
  password: "waf_password"
  ssl_mode: "disable"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300s

policies:
  sql_injection: true
  xss: true
  path_traversal: true
  command_injection: true
  rate_limiting: true
  ip_blocking: true
  rate_limit:
    requests_per_minute: 100
    burst_size: 20
  blocked_ips: []
  allowed_ips:
    - "127.0.0.1"
    - "::1"

logging:
  level: "info"
  format: "json"
  output: "stdout"
  file_path: "/var/log/waf.log"

security_headers:
  enable: true
  headers:
    X-Frame-Options: "DENY"
    X-Content-Type-Options: "nosniff"
`

	// Write to temporary file
	tmpFile, err := os.CreateTemp("", "config_test_*.yml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.WriteString(configContent); err != nil {
		t.Fatalf("Failed to write config: %v", err)
	}
	tmpFile.Close()

	// Test loading
	cfg, err := Load(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// Validate loaded config
	if cfg.Server.Port != 8080 {
		t.Errorf("Expected port 8080, got %d", cfg.Server.Port)
	}

	if cfg.Mode != "log" {
		t.Errorf("Expected mode 'log', got %s", cfg.Mode)
	}

	if cfg.Database.Host != "localhost" {
		t.Errorf("Expected database host 'localhost', got %s", cfg.Database.Host)
	}

	if !cfg.Policies.SQLInjection {
		t.Error("Expected SQL injection policy to be enabled")
	}

	if cfg.Logging.Level != "info" {
		t.Errorf("Expected log level 'info', got %s", cfg.Logging.Level)
	}
}

func TestValidate(t *testing.T) {
	tests := []struct {
		name    string
		config  *Config
		wantErr bool
	}{
		{
			name: "valid config",
			config: &Config{
				Server: ServerConfig{
					Port: 8080,
					Host: "0.0.0.0",
				},
				Mode: "log",
				Upstream: UpstreamConfig{
					Host:   "localhost",
					Port:   3000,
					Scheme: "http",
				},
				Database: DatabaseConfig{
					Host: "localhost",
					Port: 5432,
					Name: "waf_db",
					User: "waf_user",
				},
				Logging: LoggingConfig{
					Level:  "info",
					Format: "json",
					Output: "stdout",
				},
				Policies: PoliciesConfig{
					RateLimit: RateLimitConfig{
						RequestsPerMinute: 100,
						BurstSize:         20,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "invalid mode",
			config: &Config{
				Mode: "invalid",
				Server: ServerConfig{
					Port: 8080,
					Host: "0.0.0.0",
				},
				Upstream: UpstreamConfig{
					Host:   "localhost",
					Port:   3000,
					Scheme: "http",
				},
				Database: DatabaseConfig{
					Host: "localhost",
					Port: 5432,
					Name: "waf_db",
					User: "waf_user",
				},
				Logging: LoggingConfig{
					Level:  "info",
					Format: "json",
					Output: "stdout",
				},
			},
			wantErr: true,
		},
		{
			name: "invalid port",
			config: &Config{
				Mode: "log",
				Server: ServerConfig{
					Port: 0,
					Host: "0.0.0.0",
				},
				Upstream: UpstreamConfig{
					Host:   "localhost",
					Port:   3000,
					Scheme: "http",
				},
				Database: DatabaseConfig{
					Host: "localhost",
					Port: 5432,
					Name: "waf_db",
					User: "waf_user",
				},
				Logging: LoggingConfig{
					Level:  "info",
					Format: "json",
					Output: "stdout",
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestIsBlockMode(t *testing.T) {
	cfg := &Config{Mode: "block"}
	if !cfg.IsBlockMode() {
		t.Error("Expected IsBlockMode() to return true for block mode")
	}

	cfg.Mode = "log"
	if cfg.IsBlockMode() {
		t.Error("Expected IsBlockMode() to return false for log mode")
	}
}

func TestIsLogMode(t *testing.T) {
	cfg := &Config{Mode: "log"}
	if !cfg.IsLogMode() {
		t.Error("Expected IsLogMode() to return true for log mode")
	}

	cfg.Mode = "block"
	if cfg.IsLogMode() {
		t.Error("Expected IsLogMode() to return false for block mode")
	}
}

func TestGetUpstreamURL(t *testing.T) {
	cfg := &Config{
		Upstream: UpstreamConfig{
			Scheme: "http",
			Host:   "localhost",
			Port:   3000,
		},
	}

	expected := "http://localhost:3000"
	if got := cfg.GetUpstreamURL(); got != expected {
		t.Errorf("GetUpstreamURL() = %v, want %v", got, expected)
	}
}

func TestApplyEnvOverrides(t *testing.T) {
	// Set environment variables
	os.Setenv("WAF_MODE", "block")
	os.Setenv("WAF_DB_HOST", "testhost")
	defer func() {
		os.Unsetenv("WAF_MODE")
		os.Unsetenv("WAF_DB_HOST")
	}()

	cfg := &Config{
		Mode: "log",
		Database: DatabaseConfig{
			Host: "localhost",
		},
	}

	cfg.ApplyEnvOverrides()

	if cfg.Mode != "block" {
		t.Errorf("Expected mode to be overridden to 'block', got %s", cfg.Mode)
	}

	if cfg.Database.Host != "testhost" {
		t.Errorf("Expected database host to be overridden to 'testhost', got %s", cfg.Database.Host)
	}
}
