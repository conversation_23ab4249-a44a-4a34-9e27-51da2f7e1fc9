package config

import "time"

// DefaultConfig returns a configuration with default values
func DefaultConfig() *Config {
	return &Config{
		Server: ServerConfig{
			Port:         8080,
			Host:         "0.0.0.0",
			ReadTimeout:  30 * time.Second,
			WriteTimeout: 30 * time.Second,
			IdleTimeout:  60 * time.Second,
		},
		Mode: "log",
		Upstream: UpstreamConfig{
			Host:    "localhost",
			Port:    3000,
			Scheme:  "http",
			Timeout: 30 * time.Second,
		},
		Database: DatabaseConfig{
			Host:            "localhost",
			Port:            5432,
			Name:            "waf_db",
			User:            "waf_user",
			Password:        "waf_password",
			SSLMode:         "disable",
			MaxOpenConns:    25,
			MaxIdleConns:    5,
			ConnMaxLifetime: 300 * time.Second,
		},
		Policies: PoliciesConfig{
			SQLInjection:     true,
			XSS:              true,
			PathTraversal:    true,
			CommandInjection: true,
			RateLimiting:     true,
			IPBlocking:       true,
			RateLimit: RateLimitConfig{
				RequestsPerMinute: 100,
				BurstSize:         20,
			},
			BlockedIPs: []string{},
			AllowedIPs: []string{"127.0.0.1", "::1"},
		},
		Logging: LoggingConfig{
			Level:    "info",
			Format:   "json",
			Output:   "stdout",
			FilePath: "/var/log/waf.log",
		},
		SecurityHeaders: SecurityHeadersConfig{
			Enable: true,
			Headers: map[string]string{
				"X-Frame-Options":           "DENY",
				"X-Content-Type-Options":    "nosniff",
				"X-XSS-Protection":          "1; mode=block",
				"Strict-Transport-Security": "max-age=31536000; includeSubDomains",
				"Content-Security-Policy":   "default-src 'self'",
			},
		},
	}
}

// MergeWithDefaults merges the provided config with default values
func MergeWithDefaults(config *Config) *Config {
	defaults := DefaultConfig()

	// Merge server config
	if config.Server.Port == 0 {
		config.Server.Port = defaults.Server.Port
	}
	if config.Server.Host == "" {
		config.Server.Host = defaults.Server.Host
	}
	if config.Server.ReadTimeout == 0 {
		config.Server.ReadTimeout = defaults.Server.ReadTimeout
	}
	if config.Server.WriteTimeout == 0 {
		config.Server.WriteTimeout = defaults.Server.WriteTimeout
	}
	if config.Server.IdleTimeout == 0 {
		config.Server.IdleTimeout = defaults.Server.IdleTimeout
	}

	// Merge mode
	if config.Mode == "" {
		config.Mode = defaults.Mode
	}

	// Merge upstream config
	if config.Upstream.Host == "" {
		config.Upstream.Host = defaults.Upstream.Host
	}
	if config.Upstream.Port == 0 {
		config.Upstream.Port = defaults.Upstream.Port
	}
	if config.Upstream.Scheme == "" {
		config.Upstream.Scheme = defaults.Upstream.Scheme
	}
	if config.Upstream.Timeout == 0 {
		config.Upstream.Timeout = defaults.Upstream.Timeout
	}

	// Merge database config
	if config.Database.Host == "" {
		config.Database.Host = defaults.Database.Host
	}
	if config.Database.Port == 0 {
		config.Database.Port = defaults.Database.Port
	}
	if config.Database.Name == "" {
		config.Database.Name = defaults.Database.Name
	}
	if config.Database.User == "" {
		config.Database.User = defaults.Database.User
	}
	if config.Database.Password == "" {
		config.Database.Password = defaults.Database.Password
	}
	if config.Database.SSLMode == "" {
		config.Database.SSLMode = defaults.Database.SSLMode
	}
	if config.Database.MaxOpenConns == 0 {
		config.Database.MaxOpenConns = defaults.Database.MaxOpenConns
	}
	if config.Database.MaxIdleConns == 0 {
		config.Database.MaxIdleConns = defaults.Database.MaxIdleConns
	}
	if config.Database.ConnMaxLifetime == 0 {
		config.Database.ConnMaxLifetime = defaults.Database.ConnMaxLifetime
	}

	// Merge policies config
	if config.Policies.RateLimit.RequestsPerMinute == 0 {
		config.Policies.RateLimit.RequestsPerMinute = defaults.Policies.RateLimit.RequestsPerMinute
	}
	if config.Policies.RateLimit.BurstSize == 0 {
		config.Policies.RateLimit.BurstSize = defaults.Policies.RateLimit.BurstSize
	}
	if len(config.Policies.AllowedIPs) == 0 {
		config.Policies.AllowedIPs = defaults.Policies.AllowedIPs
	}

	// Merge logging config
	if config.Logging.Level == "" {
		config.Logging.Level = defaults.Logging.Level
	}
	if config.Logging.Format == "" {
		config.Logging.Format = defaults.Logging.Format
	}
	if config.Logging.Output == "" {
		config.Logging.Output = defaults.Logging.Output
	}
	if config.Logging.FilePath == "" {
		config.Logging.FilePath = defaults.Logging.FilePath
	}

	// Merge security headers
	if config.SecurityHeaders.Headers == nil {
		config.SecurityHeaders.Headers = defaults.SecurityHeaders.Headers
	}

	return config
}
