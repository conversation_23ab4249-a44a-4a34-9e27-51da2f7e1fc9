package database

import (
	"encoding/json"
	"net"
	"time"

	"github.com/google/uuid"
)

// RequestStatus represents the status of a request
type RequestStatus string

const (
	RequestStatusAllowed RequestStatus = "allowed"
	RequestStatusBlocked RequestStatus = "blocked"
	RequestStatusLogged  RequestStatus = "logged"
)

// PolicyAction represents the action to take for a policy
type PolicyAction string

const (
	PolicyActionBlock PolicyAction = "block"
	PolicyActionLog   PolicyAction = "log"
	PolicyActionAllow PolicyAction = "allow"
)

// ThreatLevel represents the severity of a threat
type ThreatLevel string

const (
	ThreatLevelLow      ThreatLevel = "low"
	ThreatLevelMedium   ThreatLevel = "medium"
	ThreatLevelHigh     ThreatLevel = "high"
	ThreatLevelCritical ThreatLevel = "critical"
)

// Request represents an HTTP request record
type Request struct {
	ID             uuid.UUID       `json:"id" db:"id"`
	Timestamp      time.Time       `json:"timestamp" db:"timestamp"`
	ClientIP       net.IP          `json:"client_ip" db:"client_ip"`
	Method         string          `json:"method" db:"method"`
	URL            string          `json:"url" db:"url"`
	UserAgent      *string         `json:"user_agent" db:"user_agent"`
	Headers        json.RawMessage `json:"headers" db:"headers"`
	Body           *string         `json:"body" db:"body"`
	QueryParams    json.RawMessage `json:"query_params" db:"query_params"`
	Status         RequestStatus   `json:"status" db:"status"`
	ResponseStatus *int            `json:"response_status" db:"response_status"`
	ResponseTimeMs *int            `json:"response_time_ms" db:"response_time_ms"`
	UpstreamHost   *string         `json:"upstream_host" db:"upstream_host"`
	CreatedAt      time.Time       `json:"created_at" db:"created_at"`
}

// SecurityEvent represents a detected security threat
type SecurityEvent struct {
	ID              uuid.UUID    `json:"id" db:"id"`
	RequestID       uuid.UUID    `json:"request_id" db:"request_id"`
	EventType       string       `json:"event_type" db:"event_type"`
	ThreatLevel     ThreatLevel  `json:"threat_level" db:"threat_level"`
	Description     string       `json:"description" db:"description"`
	MatchedPattern  *string      `json:"matched_pattern" db:"matched_pattern"`
	PolicyID        *uuid.UUID   `json:"policy_id" db:"policy_id"`
	ActionTaken     PolicyAction `json:"action_taken" db:"action_taken"`
	ConfidenceScore *float64     `json:"confidence_score" db:"confidence_score"`
	CreatedAt       time.Time    `json:"created_at" db:"created_at"`
}

// Policy represents a WAF security policy
type Policy struct {
	ID          uuid.UUID    `json:"id" db:"id"`
	Name        string       `json:"name" db:"name"`
	Category    string       `json:"category" db:"category"`
	Pattern     string       `json:"pattern" db:"pattern"`
	Action      PolicyAction `json:"action" db:"action"`
	Enabled     bool         `json:"enabled" db:"enabled"`
	Priority    int          `json:"priority" db:"priority"`
	Description *string      `json:"description" db:"description"`
	CreatedAt   time.Time    `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at" db:"updated_at"`
}

// BlockedIP represents a blocked IP address
type BlockedIP struct {
	ID           uuid.UUID  `json:"id" db:"id"`
	IPAddress    net.IP     `json:"ip_address" db:"ip_address"`
	Reason       string     `json:"reason" db:"reason"`
	BlockedUntil *time.Time `json:"blocked_until" db:"blocked_until"`
	CreatedAt    time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at" db:"updated_at"`
}

// RateLimit represents rate limiting data for an IP
type RateLimit struct {
	ID           uuid.UUID `json:"id" db:"id"`
	IPAddress    net.IP    `json:"ip_address" db:"ip_address"`
	WindowStart  time.Time `json:"window_start" db:"window_start"`
	RequestCount int       `json:"request_count" db:"request_count"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

// WAFStatistics represents aggregated WAF statistics
type WAFStatistics struct {
	ID               uuid.UUID       `json:"id" db:"id"`
	Date             time.Time       `json:"date" db:"date"`
	TotalRequests    int64           `json:"total_requests" db:"total_requests"`
	BlockedRequests  int64           `json:"blocked_requests" db:"blocked_requests"`
	AllowedRequests  int64           `json:"allowed_requests" db:"allowed_requests"`
	UniqueIPs        int             `json:"unique_ips" db:"unique_ips"`
	TopThreatTypes   json.RawMessage `json:"top_threat_types" db:"top_threat_types"`
	CreatedAt        time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time       `json:"updated_at" db:"updated_at"`
}
