package database

import (
	"context"
	"database/sql"
	"net"
	"time"

	"github.com/google/uuid"
)

// Repository provides database operations
type Repository struct {
	db *DB
}

// NewRepository creates a new repository instance
func NewRepository(db *DB) *Repository {
	return &Repository{db: db}
}

// ExecContext executes a query without returning any rows
func (r *Repository) ExecContext(ctx context.Context, query string, args ...interface{}) (sql.Result, error) {
	return r.db.ExecContext(ctx, query, args...)
}

// QueryContext executes a query that returns rows
func (r *Repository) QueryContext(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	return r.db.QueryContext(ctx, query, args...)
}

// QueryRowContext executes a query that is expected to return at most one row
func (r *Repository) QueryRowContext(ctx context.Context, query string, args ...interface{}) *sql.Row {
	return r.db.QueryRowContext(ctx, query, args...)
}

// Request operations

// CreateRequest inserts a new request record
func (r *Repository) CreateRequest(ctx context.Context, req *Request) error {
	query := `
		INSERT INTO requests (id, timestamp, client_ip, method, url, user_agent, headers, body, query_params, status, upstream_host)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
	`

	_, err := r.db.ExecContext(ctx, query,
		req.ID, req.Timestamp, req.ClientIP, req.Method, req.URL,
		req.UserAgent, req.Headers, req.Body, req.QueryParams,
		req.Status, req.UpstreamHost,
	)

	return err
}

// UpdateRequestResponse updates the response information for a request
func (r *Repository) UpdateRequestResponse(ctx context.Context, requestID uuid.UUID, status int, responseTime int) error {
	query := `
		UPDATE requests 
		SET response_status = $1, response_time_ms = $2
		WHERE id = $3
	`

	_, err := r.db.ExecContext(ctx, query, status, responseTime, requestID)
	return err
}

// GetRequestByID retrieves a request by its ID
func (r *Repository) GetRequestByID(ctx context.Context, id uuid.UUID) (*Request, error) {
	query := `
		SELECT id, timestamp, client_ip, method, url, user_agent, headers, body, 
		       query_params, status, response_status, response_time_ms, upstream_host, created_at
		FROM requests WHERE id = $1
	`

	req := &Request{}
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&req.ID, &req.Timestamp, &req.ClientIP, &req.Method, &req.URL,
		&req.UserAgent, &req.Headers, &req.Body, &req.QueryParams,
		&req.Status, &req.ResponseStatus, &req.ResponseTimeMs,
		&req.UpstreamHost, &req.CreatedAt,
	)

	if err != nil {
		return nil, err
	}

	return req, nil
}

// Security Event operations

// CreateSecurityEvent inserts a new security event
func (r *Repository) CreateSecurityEvent(ctx context.Context, event *SecurityEvent) error {
	query := `
		INSERT INTO security_events (id, request_id, event_type, threat_level, description, 
		                           matched_pattern, policy_id, action_taken, confidence_score)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
	`

	_, err := r.db.ExecContext(ctx, query,
		event.ID, event.RequestID, event.EventType, event.ThreatLevel,
		event.Description, event.MatchedPattern, event.PolicyID,
		event.ActionTaken, event.ConfidenceScore,
	)

	return err
}

// GetSecurityEventsByRequestID retrieves security events for a request
func (r *Repository) GetSecurityEventsByRequestID(ctx context.Context, requestID uuid.UUID) ([]*SecurityEvent, error) {
	query := `
		SELECT id, request_id, event_type, threat_level, description, matched_pattern,
		       policy_id, action_taken, confidence_score, created_at
		FROM security_events WHERE request_id = $1
		ORDER BY created_at DESC
	`

	rows, err := r.db.QueryContext(ctx, query, requestID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var events []*SecurityEvent
	for rows.Next() {
		event := &SecurityEvent{}
		err := rows.Scan(
			&event.ID, &event.RequestID, &event.EventType, &event.ThreatLevel,
			&event.Description, &event.MatchedPattern, &event.PolicyID,
			&event.ActionTaken, &event.ConfidenceScore, &event.CreatedAt,
		)
		if err != nil {
			return nil, err
		}
		events = append(events, event)
	}

	return events, rows.Err()
}

// Policy operations

// GetPoliciesByCategory retrieves policies by category
func (r *Repository) GetPoliciesByCategory(ctx context.Context, category string) ([]*Policy, error) {
	query := `
		SELECT id, name, category, pattern, action, enabled, priority, description, created_at, updated_at
		FROM policies 
		WHERE category = $1 AND enabled = true
		ORDER BY priority DESC
	`

	rows, err := r.db.QueryContext(ctx, query, category)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var policies []*Policy
	for rows.Next() {
		policy := &Policy{}
		err := rows.Scan(
			&policy.ID, &policy.Name, &policy.Category, &policy.Pattern,
			&policy.Action, &policy.Enabled, &policy.Priority,
			&policy.Description, &policy.CreatedAt, &policy.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		policies = append(policies, policy)
	}

	return policies, rows.Err()
}

// GetAllEnabledPolicies retrieves all enabled policies
func (r *Repository) GetAllEnabledPolicies(ctx context.Context) ([]*Policy, error) {
	query := `
		SELECT id, name, category, pattern, action, enabled, priority, description, created_at, updated_at
		FROM policies 
		WHERE enabled = true
		ORDER BY priority DESC, category, name
	`

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var policies []*Policy
	for rows.Next() {
		policy := &Policy{}
		err := rows.Scan(
			&policy.ID, &policy.Name, &policy.Category, &policy.Pattern,
			&policy.Action, &policy.Enabled, &policy.Priority,
			&policy.Description, &policy.CreatedAt, &policy.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		policies = append(policies, policy)
	}

	return policies, rows.Err()
}

// Blocked IP operations

// IsIPBlocked checks if an IP is currently blocked
func (r *Repository) IsIPBlocked(ctx context.Context, ip net.IP) (bool, error) {
	query := `
		SELECT COUNT(*) FROM blocked_ips 
		WHERE ip_address = $1 AND (blocked_until IS NULL OR blocked_until > NOW())
	`

	var count int
	err := r.db.QueryRowContext(ctx, query, ip).Scan(&count)
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// BlockIP adds an IP to the blocked list
func (r *Repository) BlockIP(ctx context.Context, ip net.IP, reason string, duration *time.Duration) error {
	var blockedUntil *time.Time
	if duration != nil {
		until := time.Now().Add(*duration)
		blockedUntil = &until
	}

	query := `
		INSERT INTO blocked_ips (id, ip_address, reason, blocked_until)
		VALUES ($1, $2, $3, $4)
		ON CONFLICT (ip_address) DO UPDATE SET
			reason = EXCLUDED.reason,
			blocked_until = EXCLUDED.blocked_until,
			updated_at = NOW()
	`

	_, err := r.db.ExecContext(ctx, query, uuid.New(), ip, reason, blockedUntil)
	return err
}

// Rate Limit operations

// GetRateLimit retrieves current rate limit data for an IP
func (r *Repository) GetRateLimit(ctx context.Context, ip net.IP, windowStart time.Time) (*RateLimit, error) {
	query := `
		SELECT id, ip_address, window_start, request_count, created_at, updated_at
		FROM rate_limits 
		WHERE ip_address = $1 AND window_start = $2
	`

	rl := &RateLimit{}
	err := r.db.QueryRowContext(ctx, query, ip, windowStart).Scan(
		&rl.ID, &rl.IPAddress, &rl.WindowStart, &rl.RequestCount,
		&rl.CreatedAt, &rl.UpdatedAt,
	)

	if err == sql.ErrNoRows {
		return nil, nil
	}

	return rl, err
}

// UpdateRateLimit updates or creates rate limit data
func (r *Repository) UpdateRateLimit(ctx context.Context, ip net.IP, windowStart time.Time) error {
	query := `
		INSERT INTO rate_limits (id, ip_address, window_start, request_count)
		VALUES ($1, $2, $3, 1)
		ON CONFLICT (ip_address, window_start) DO UPDATE SET
			request_count = rate_limits.request_count + 1,
			updated_at = NOW()
	`

	_, err := r.db.ExecContext(ctx, query, uuid.New(), ip, windowStart)
	return err
}

// CleanupOldRateLimits removes old rate limit records
func (r *Repository) CleanupOldRateLimits(ctx context.Context, before time.Time) error {
	query := `DELETE FROM rate_limits WHERE window_start < $1`
	_, err := r.db.ExecContext(ctx, query, before)
	return err
}

// Health checks the health of the repository
func (r *Repository) Health() error {
	return r.db.Health()
}
