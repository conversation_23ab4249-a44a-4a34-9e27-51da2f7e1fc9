package logging

import (
	"fmt"
	"io"
	"os"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/waf-fav/internal/config"
)

// Logger represents the application logger
type Logger struct {
	*logrus.Logger
	config *config.LoggingConfig
}

// Fields represents log fields
type Fields map[string]interface{}

// New creates a new logger instance
func New(cfg *config.LoggingConfig) (*Logger, error) {
	logger := logrus.New()

	// Set log level
	level, err := logrus.ParseLevel(cfg.Level)
	if err != nil {
		return nil, fmt.Errorf("invalid log level: %w", err)
	}
	logger.SetLevel(level)

	// Set log format
	switch cfg.Format {
	case "json":
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339,
			FieldMap: logrus.FieldMap{
				logrus.FieldKeyTime:  "timestamp",
				logrus.FieldKeyLevel: "level",
				logrus.FieldKeyMsg:   "message",
			},
		})
	case "text":
		logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: time.RFC3339,
		})
	default:
		return nil, fmt.Errorf("invalid log format: %s", cfg.Format)
	}

	// Set output
	var output io.Writer
	switch cfg.Output {
	case "stdout":
		output = os.Stdout
	case "file":
		if cfg.FilePath == "" {
			return nil, fmt.Errorf("file path required for file output")
		}
		file, err := os.OpenFile(cfg.FilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return nil, fmt.Errorf("failed to open log file: %w", err)
		}
		output = file
	default:
		return nil, fmt.Errorf("invalid log output: %s", cfg.Output)
	}

	logger.SetOutput(output)

	return &Logger{
		Logger: logger,
		config: cfg,
	}, nil
}

// WithFields creates a new logger entry with fields
func (l *Logger) WithFields(fields Fields) *logrus.Entry {
	return l.Logger.WithFields(logrus.Fields(fields))
}

// WithField creates a new logger entry with a single field
func (l *Logger) WithField(key string, value interface{}) *logrus.Entry {
	return l.Logger.WithField(key, value)
}

// WithError creates a new logger entry with an error field
func (l *Logger) WithError(err error) *logrus.Entry {
	return l.Logger.WithError(err)
}

// Security logs security-related events
func (l *Logger) Security(level logrus.Level, message string, fields Fields) {
	entry := l.WithFields(fields).WithField("category", "security")
	entry.Log(level, message)
}

// Request logs HTTP request information
func (l *Logger) Request(method, url, clientIP, userAgent string, statusCode int, duration time.Duration, requestID string) {
	l.WithFields(Fields{
		"category":    "request",
		"method":      method,
		"url":         url,
		"client_ip":   clientIP,
		"user_agent":  userAgent,
		"status_code": statusCode,
		"duration_ms": duration.Milliseconds(),
		"request_id":  requestID,
	}).Info("HTTP request processed")
}

// SecurityEvent logs a security event
func (l *Logger) SecurityEvent(eventType, description, clientIP, requestID string, threatLevel string, blocked bool) {
	l.WithFields(Fields{
		"category":     "security_event",
		"event_type":   eventType,
		"description":  description,
		"client_ip":    clientIP,
		"request_id":   requestID,
		"threat_level": threatLevel,
		"blocked":      blocked,
	}).Warn("Security event detected")
}

// PolicyMatch logs when a policy matches
func (l *Logger) PolicyMatch(policyName, policyCategory, pattern, clientIP, requestID string, action string) {
	l.WithFields(Fields{
		"category":        "policy_match",
		"policy_name":     policyName,
		"policy_category": policyCategory,
		"pattern":         pattern,
		"client_ip":       clientIP,
		"request_id":      requestID,
		"action":          action,
	}).Info("Policy matched")
}

// RateLimit logs rate limiting events
func (l *Logger) RateLimit(clientIP, requestID string, requestCount, limit int) {
	l.WithFields(Fields{
		"category":      "rate_limit",
		"client_ip":     clientIP,
		"request_id":    requestID,
		"request_count": requestCount,
		"limit":         limit,
	}).Warn("Rate limit exceeded")
}

// IPBlocked logs IP blocking events
func (l *Logger) IPBlocked(clientIP, reason, requestID string, duration *time.Duration) {
	fields := Fields{
		"category":   "ip_blocked",
		"client_ip":  clientIP,
		"reason":     reason,
		"request_id": requestID,
	}

	if duration != nil {
		fields["duration"] = duration.String()
	} else {
		fields["permanent"] = true
	}

	l.WithFields(fields).Warn("IP address blocked")
}

// Error logs error messages with context
func (l *Logger) Error(message string, err error, fields Fields) {
	entry := l.WithError(err)
	if fields != nil {
		entry = entry.WithFields(logrus.Fields(fields))
	}
	entry.Error(message)
}

// Warn logs warning messages with context
func (l *Logger) Warn(message string, fields Fields) {
	entry := l.Logger.WithFields(logrus.Fields(fields))
	entry.Warn(message)
}

// Info logs info messages with context
func (l *Logger) Info(message string, fields Fields) {
	entry := l.Logger.WithFields(logrus.Fields(fields))
	entry.Info(message)
}

// Debug logs debug messages with context
func (l *Logger) Debug(message string, fields Fields) {
	entry := l.Logger.WithFields(logrus.Fields(fields))
	entry.Debug(message)
}

// Startup logs application startup information
func (l *Logger) Startup(version, mode string, port int) {
	l.WithFields(Fields{
		"category": "startup",
		"version":  version,
		"mode":     mode,
		"port":     port,
	}).Info("WAF application started")
}

// Shutdown logs application shutdown information
func (l *Logger) Shutdown(reason string) {
	l.WithFields(Fields{
		"category": "shutdown",
		"reason":   reason,
	}).Info("WAF application shutting down")
}

// DatabaseConnection logs database connection events
func (l *Logger) DatabaseConnection(event string, host string, database string, err error) {
	fields := Fields{
		"category": "database",
		"event":    event,
		"host":     host,
		"database": database,
	}

	if err != nil {
		l.WithFields(fields).WithError(err).Error("Database connection event")
	} else {
		l.WithFields(fields).Info("Database connection event")
	}
}

// PolicyReload logs policy reload events
func (l *Logger) PolicyReload(count int, err error) {
	fields := Fields{
		"category":     "policy_reload",
		"policy_count": count,
	}

	if err != nil {
		l.WithFields(fields).WithError(err).Error("Failed to reload policies")
	} else {
		l.WithFields(fields).Info("Policies reloaded successfully")
	}
}

// UpstreamError logs upstream server errors
func (l *Logger) UpstreamError(url, method, clientIP, requestID string, err error, statusCode int) {
	l.WithFields(Fields{
		"category":    "upstream_error",
		"url":         url,
		"method":      method,
		"client_ip":   clientIP,
		"request_id":  requestID,
		"status_code": statusCode,
	}).WithError(err).Error("Upstream server error")
}

// Performance logs performance metrics
func (l *Logger) Performance(operation string, duration time.Duration, fields Fields) {
	if fields == nil {
		fields = make(Fields)
	}
	fields["category"] = "performance"
	fields["operation"] = operation
	fields["duration_ms"] = duration.Milliseconds()

	l.WithFields(fields).Info("Performance metric")
}
