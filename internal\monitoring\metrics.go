package monitoring

import (
	"context"
	"sync"
	"time"

	"github.com/waf-fav/internal/database"
)

// Metrics represents the metrics collector
type Metrics struct {
	mu                    sync.RWMutex
	requestsTotal         map[string]int64
	requestsBlocked       map[string]int64
	requestsAllowed       map[string]int64
	responseTimeSum       map[string]float64
	responseTimeCount     map[string]int64
	threatsByType         map[string]int64
	requestsByIP          map[string]int64
	requestsByUserAgent   map[string]int64
	policyMatches         map[string]int64
	upstreamErrors        int64
	rateLimitExceeded     int64
	ipBlocks              int64
	startTime             time.Time
	repository            *database.Repository
}

// MetricsSummary represents a summary of metrics
type MetricsSummary struct {
	RequestsTotal       int64                    `json:"requests_total"`
	RequestsBlocked     int64                    `json:"requests_blocked"`
	RequestsAllowed     int64                    `json:"requests_allowed"`
	BlockRate           float64                  `json:"block_rate"`
	AverageResponseTime float64                  `json:"average_response_time_ms"`
	TopThreats          []ThreatMetric           `json:"top_threats"`
	TopIPs              []IPMetric               `json:"top_ips"`
	TopUserAgents       []UserAgentMetric        `json:"top_user_agents"`
	PolicyMatches       []PolicyMatchMetric      `json:"policy_matches"`
	UpstreamErrors      int64                    `json:"upstream_errors"`
	RateLimitExceeded   int64                    `json:"rate_limit_exceeded"`
	IPBlocks            int64                    `json:"ip_blocks"`
	Uptime              string                   `json:"uptime"`
	RequestsByHour      map[string]int64         `json:"requests_by_hour"`
}

// ThreatMetric represents threat statistics
type ThreatMetric struct {
	Type  string `json:"type"`
	Count int64  `json:"count"`
}

// IPMetric represents IP statistics
type IPMetric struct {
	IP    string `json:"ip"`
	Count int64  `json:"count"`
}

// UserAgentMetric represents user agent statistics
type UserAgentMetric struct {
	UserAgent string `json:"user_agent"`
	Count     int64  `json:"count"`
}

// PolicyMatchMetric represents policy match statistics
type PolicyMatchMetric struct {
	PolicyName string `json:"policy_name"`
	Count      int64  `json:"count"`
}

// New creates a new metrics collector
func New(repo *database.Repository) *Metrics {
	return &Metrics{
		requestsTotal:       make(map[string]int64),
		requestsBlocked:     make(map[string]int64),
		requestsAllowed:     make(map[string]int64),
		responseTimeSum:     make(map[string]float64),
		responseTimeCount:   make(map[string]int64),
		threatsByType:       make(map[string]int64),
		requestsByIP:        make(map[string]int64),
		requestsByUserAgent: make(map[string]int64),
		policyMatches:       make(map[string]int64),
		startTime:           time.Now(),
		repository:          repo,
	}
}

// RecordRequest records a request metric
func (m *Metrics) RecordRequest(method, status, clientIP, userAgent string, responseTime time.Duration) {
	m.mu.Lock()
	defer m.mu.Unlock()

	key := method
	m.requestsTotal[key]++
	
	if status == "blocked" {
		m.requestsBlocked[key]++
	} else {
		m.requestsAllowed[key]++
	}

	// Record response time
	m.responseTimeSum[key] += float64(responseTime.Milliseconds())
	m.responseTimeCount[key]++

	// Record by IP
	m.requestsByIP[clientIP]++

	// Record by user agent (truncate long user agents)
	if len(userAgent) > 100 {
		userAgent = userAgent[:100] + "..."
	}
	m.requestsByUserAgent[userAgent]++
}

// RecordThreat records a threat detection
func (m *Metrics) RecordThreat(threatType string) {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.threatsByType[threatType]++
}

// RecordPolicyMatch records a policy match
func (m *Metrics) RecordPolicyMatch(policyName string) {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.policyMatches[policyName]++
}

// RecordUpstreamError records an upstream error
func (m *Metrics) RecordUpstreamError() {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.upstreamErrors++
}

// RecordRateLimitExceeded records a rate limit exceeded event
func (m *Metrics) RecordRateLimitExceeded() {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.rateLimitExceeded++
}

// RecordIPBlock records an IP block event
func (m *Metrics) RecordIPBlock() {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.ipBlocks++
}

// GetSummary returns a summary of all metrics
func (m *Metrics) GetSummary() *MetricsSummary {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var totalRequests, totalBlocked, totalAllowed int64
	var totalResponseTime float64
	var totalResponseCount int64

	// Calculate totals
	for _, count := range m.requestsTotal {
		totalRequests += count
	}
	for _, count := range m.requestsBlocked {
		totalBlocked += count
	}
	for _, count := range m.requestsAllowed {
		totalAllowed += count
	}
	for _, sum := range m.responseTimeSum {
		totalResponseTime += sum
	}
	for _, count := range m.responseTimeCount {
		totalResponseCount += count
	}

	// Calculate block rate
	var blockRate float64
	if totalRequests > 0 {
		blockRate = float64(totalBlocked) / float64(totalRequests) * 100
	}

	// Calculate average response time
	var avgResponseTime float64
	if totalResponseCount > 0 {
		avgResponseTime = totalResponseTime / float64(totalResponseCount)
	}

	// Get top threats
	topThreats := m.getTopThreats(10)

	// Get top IPs
	topIPs := m.getTopIPs(10)

	// Get top user agents
	topUserAgents := m.getTopUserAgents(10)

	// Get policy matches
	policyMatches := m.getPolicyMatches(10)

	// Calculate uptime
	uptime := time.Since(m.startTime).String()

	// Get requests by hour (placeholder - would need time-based tracking)
	requestsByHour := make(map[string]int64)

	return &MetricsSummary{
		RequestsTotal:       totalRequests,
		RequestsBlocked:     totalBlocked,
		RequestsAllowed:     totalAllowed,
		BlockRate:           blockRate,
		AverageResponseTime: avgResponseTime,
		TopThreats:          topThreats,
		TopIPs:              topIPs,
		TopUserAgents:       topUserAgents,
		PolicyMatches:       policyMatches,
		UpstreamErrors:      m.upstreamErrors,
		RateLimitExceeded:   m.rateLimitExceeded,
		IPBlocks:            m.ipBlocks,
		Uptime:              uptime,
		RequestsByHour:      requestsByHour,
	}
}

// getTopThreats returns the top threats by count
func (m *Metrics) getTopThreats(limit int) []ThreatMetric {
	threats := make([]ThreatMetric, 0, len(m.threatsByType))
	for threatType, count := range m.threatsByType {
		threats = append(threats, ThreatMetric{
			Type:  threatType,
			Count: count,
		})
	}

	// Sort by count (simple bubble sort for small datasets)
	for i := 0; i < len(threats)-1; i++ {
		for j := 0; j < len(threats)-i-1; j++ {
			if threats[j].Count < threats[j+1].Count {
				threats[j], threats[j+1] = threats[j+1], threats[j]
			}
		}
	}

	if len(threats) > limit {
		threats = threats[:limit]
	}

	return threats
}

// getTopIPs returns the top IPs by request count
func (m *Metrics) getTopIPs(limit int) []IPMetric {
	ips := make([]IPMetric, 0, len(m.requestsByIP))
	for ip, count := range m.requestsByIP {
		ips = append(ips, IPMetric{
			IP:    ip,
			Count: count,
		})
	}

	// Sort by count
	for i := 0; i < len(ips)-1; i++ {
		for j := 0; j < len(ips)-i-1; j++ {
			if ips[j].Count < ips[j+1].Count {
				ips[j], ips[j+1] = ips[j+1], ips[j]
			}
		}
	}

	if len(ips) > limit {
		ips = ips[:limit]
	}

	return ips
}

// getTopUserAgents returns the top user agents by request count
func (m *Metrics) getTopUserAgents(limit int) []UserAgentMetric {
	userAgents := make([]UserAgentMetric, 0, len(m.requestsByUserAgent))
	for ua, count := range m.requestsByUserAgent {
		userAgents = append(userAgents, UserAgentMetric{
			UserAgent: ua,
			Count:     count,
		})
	}

	// Sort by count
	for i := 0; i < len(userAgents)-1; i++ {
		for j := 0; j < len(userAgents)-i-1; j++ {
			if userAgents[j].Count < userAgents[j+1].Count {
				userAgents[j], userAgents[j+1] = userAgents[j+1], userAgents[j]
			}
		}
	}

	if len(userAgents) > limit {
		userAgents = userAgents[:limit]
	}

	return userAgents
}

// getPolicyMatches returns policy matches by count
func (m *Metrics) getPolicyMatches(limit int) []PolicyMatchMetric {
	matches := make([]PolicyMatchMetric, 0, len(m.policyMatches))
	for policy, count := range m.policyMatches {
		matches = append(matches, PolicyMatchMetric{
			PolicyName: policy,
			Count:      count,
		})
	}

	// Sort by count
	for i := 0; i < len(matches)-1; i++ {
		for j := 0; j < len(matches)-i-1; j++ {
			if matches[j].Count < matches[j+1].Count {
				matches[j], matches[j+1] = matches[j+1], matches[j]
			}
		}
	}

	if len(matches) > limit {
		matches = matches[:limit]
	}

	return matches
}

// Reset resets all metrics
func (m *Metrics) Reset() {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.requestsTotal = make(map[string]int64)
	m.requestsBlocked = make(map[string]int64)
	m.requestsAllowed = make(map[string]int64)
	m.responseTimeSum = make(map[string]float64)
	m.responseTimeCount = make(map[string]int64)
	m.threatsByType = make(map[string]int64)
	m.requestsByIP = make(map[string]int64)
	m.requestsByUserAgent = make(map[string]int64)
	m.policyMatches = make(map[string]int64)
	m.upstreamErrors = 0
	m.rateLimitExceeded = 0
	m.ipBlocks = 0
	m.startTime = time.Now()
}

// PersistMetrics saves current metrics to database
func (m *Metrics) PersistMetrics(ctx context.Context) error {
	// This would save metrics to the database for historical analysis
	// Implementation would depend on specific requirements
	return nil
}
