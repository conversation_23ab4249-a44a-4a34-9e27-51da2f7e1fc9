package policy

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"regexp"
	"time"

	"github.com/google/uuid"
	"github.com/waf-fav/internal/config"
	"github.com/waf-fav/internal/database"
)

// Engine represents the policy evaluation engine
type Engine struct {
	config     *config.Config
	repository *database.Repository
	policies   map[string][]*database.Policy
	regexCache map[string]*regexp.Regexp
}

// EvaluationResult represents the result of policy evaluation
type EvaluationResult struct {
	Action          database.PolicyAction
	BlockedBy       []*database.Policy
	SecurityEvents  []*database.SecurityEvent
	ShouldBlock     bool
	ShouldLog       bool
	ThreatLevel     database.ThreatLevel
	ConfidenceScore float64
}

// RequestContext contains information about the request being evaluated
type RequestContext struct {
	Method      string
	URL         string
	Headers     http.Header
	Body        string
	QueryParams map[string][]string
	ClientIP    net.IP
	UserAgent   string
	RequestID   uuid.UUID
}

// NewEngine creates a new policy engine
func NewEngine(cfg *config.Config, repo *database.Repository) *Engine {
	return &Engine{
		config:     cfg,
		repository: repo,
		policies:   make(map[string][]*database.Policy),
		regexCache: make(map[string]*regexp.Regexp),
	}
}

// LoadPolicies loads all enabled policies from the database
func (e *Engine) LoadPolicies(ctx context.Context) error {
	policies, err := e.repository.GetAllEnabledPolicies(ctx)
	if err != nil {
		return fmt.Errorf("failed to load policies: %w", err)
	}

	// Group policies by category
	e.policies = make(map[string][]*database.Policy)
	for _, policy := range policies {
		e.policies[policy.Category] = append(e.policies[policy.Category], policy)
	}

	// Pre-compile regex patterns
	e.regexCache = make(map[string]*regexp.Regexp)
	for _, policy := range policies {
		if _, exists := e.regexCache[policy.Pattern]; !exists {
			regex, err := regexp.Compile(policy.Pattern)
			if err != nil {
				// Log error but continue with other policies
				continue
			}
			e.regexCache[policy.Pattern] = regex
		}
	}

	return nil
}

// Evaluate evaluates a request against all applicable policies
func (e *Engine) Evaluate(ctx context.Context, reqCtx *RequestContext) (*EvaluationResult, error) {
	result := &EvaluationResult{
		Action:          database.PolicyActionAllow,
		BlockedBy:       []*database.Policy{},
		SecurityEvents:  []*database.SecurityEvent{},
		ShouldBlock:     false,
		ShouldLog:       false,
		ThreatLevel:     database.ThreatLevelLow,
		ConfidenceScore: 0.0,
	}

	// Check if IP is blocked
	if e.config.Policies.IPBlocking {
		blocked, err := e.repository.IsIPBlocked(ctx, reqCtx.ClientIP)
		if err != nil {
			return nil, fmt.Errorf("failed to check IP blocking: %w", err)
		}
		if blocked {
			result.Action = database.PolicyActionBlock
			result.ShouldBlock = true
			result.ThreatLevel = database.ThreatLevelHigh
			return result, nil
		}
	}

	// Check rate limiting
	if e.config.Policies.RateLimiting {
		if exceeded, err := e.checkRateLimit(ctx, reqCtx.ClientIP); err != nil {
			return nil, fmt.Errorf("failed to check rate limit: %w", err)
		} else if exceeded {
			result.Action = database.PolicyActionBlock
			result.ShouldBlock = true
			result.ThreatLevel = database.ThreatLevelMedium

			// Create security event for rate limiting
			event := &database.SecurityEvent{
				ID:              uuid.New(),
				RequestID:       reqCtx.RequestID,
				EventType:       "rate_limit_exceeded",
				ThreatLevel:     database.ThreatLevelMedium,
				Description:     "Rate limit exceeded",
				ActionTaken:     database.PolicyActionBlock,
				ConfidenceScore: &[]float64{1.0}[0],
			}
			result.SecurityEvents = append(result.SecurityEvents, event)
			return result, nil
		}
	}

	// Evaluate content-based policies
	if err := e.evaluateContentPolicies(ctx, reqCtx, result); err != nil {
		return nil, fmt.Errorf("failed to evaluate content policies: %w", err)
	}

	// Determine final action based on configuration mode
	if e.config.IsBlockMode() && len(result.BlockedBy) > 0 {
		result.ShouldBlock = true
		result.Action = database.PolicyActionBlock
	} else if len(result.SecurityEvents) > 0 {
		result.ShouldLog = true
		if result.Action == database.PolicyActionAllow {
			result.Action = database.PolicyActionLog
		}
	}

	return result, nil
}

// evaluateContentPolicies evaluates content-based security policies
func (e *Engine) evaluateContentPolicies(ctx context.Context, reqCtx *RequestContext, result *EvaluationResult) error {
	// Prepare content for evaluation
	content := e.prepareContent(reqCtx)

	// Evaluate each policy category
	categories := []string{
		"sql_injection",
		"xss",
		"path_traversal",
		"command_injection",
		"file_inclusion",
		"http_method",
		"user_agent",
		"header_injection",
		"size_limit",
		"encoding",
		"protocol",
	}

	for _, category := range categories {
		if !e.isCategoryEnabled(category) {
			continue
		}

		policies := e.policies[category]
		for _, policy := range policies {
			if e.evaluatePolicy(policy, content, reqCtx) {
				// Policy matched - create security event
				event := &database.SecurityEvent{
					ID:              uuid.New(),
					RequestID:       reqCtx.RequestID,
					EventType:       category,
					ThreatLevel:     e.getThreatLevel(category),
					Description:     fmt.Sprintf("Policy '%s' matched", policy.Name),
					MatchedPattern:  &policy.Pattern,
					PolicyID:        &policy.ID,
					ActionTaken:     policy.Action,
					ConfidenceScore: e.calculateConfidence(policy, content),
				}

				result.SecurityEvents = append(result.SecurityEvents, event)

				// Update threat level
				if event.ThreatLevel > result.ThreatLevel {
					result.ThreatLevel = event.ThreatLevel
				}

				// Check if this policy should block
				if policy.Action == database.PolicyActionBlock {
					result.BlockedBy = append(result.BlockedBy, policy)
				}

				// Update confidence score (take maximum)
				if event.ConfidenceScore != nil && *event.ConfidenceScore > result.ConfidenceScore {
					result.ConfidenceScore = *event.ConfidenceScore
				}
			}
		}
	}

	return nil
}

// prepareContent prepares request content for policy evaluation
func (e *Engine) prepareContent(reqCtx *RequestContext) map[string]string {
	content := make(map[string]string)

	// URL and query parameters
	content["url"] = reqCtx.URL
	content["query"] = ""
	for key, values := range reqCtx.QueryParams {
		for _, value := range values {
			content["query"] += fmt.Sprintf("%s=%s&", key, value)
		}
	}

	// Headers
	content["headers"] = ""
	for key, values := range reqCtx.Headers {
		for _, value := range values {
			content["headers"] += fmt.Sprintf("%s: %s\n", key, value)
		}
	}

	// Body
	content["body"] = reqCtx.Body

	// User agent
	content["user_agent"] = reqCtx.UserAgent

	// Method
	content["method"] = reqCtx.Method

	// Combined content for general pattern matching
	content["all"] = fmt.Sprintf("%s %s %s %s %s",
		reqCtx.Method, reqCtx.URL, content["query"], content["headers"], reqCtx.Body)

	return content
}

// evaluatePolicy evaluates a single policy against request content
func (e *Engine) evaluatePolicy(policy *database.Policy, content map[string]string, reqCtx *RequestContext) bool {
	regex, exists := e.regexCache[policy.Pattern]
	if !exists {
		return false
	}

	// Check different content types based on policy category
	switch policy.Category {
	case "http_method":
		return regex.MatchString(reqCtx.Method)
	case "user_agent":
		return regex.MatchString(reqCtx.UserAgent)
	case "size_limit":
		return regex.MatchString(reqCtx.Body) || regex.MatchString(reqCtx.URL)
	default:
		// Check all content for general policies
		for _, text := range content {
			if regex.MatchString(text) {
				return true
			}
		}
	}

	return false
}

// checkRateLimit checks if the request exceeds rate limits
func (e *Engine) checkRateLimit(ctx context.Context, ip net.IP) (bool, error) {
	// Calculate current window start (minute-based)
	now := time.Now()
	windowStart := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), 0, 0, now.Location())

	// Get current rate limit data
	rateLimit, err := e.repository.GetRateLimit(ctx, ip, windowStart)
	if err != nil {
		return false, err
	}

	// Check if limit is exceeded
	if rateLimit != nil && rateLimit.RequestCount >= e.config.Policies.RateLimit.RequestsPerMinute {
		return true, nil
	}

	// Update rate limit counter
	if err := e.repository.UpdateRateLimit(ctx, ip, windowStart); err != nil {
		return false, err
	}

	return false, nil
}

// isCategoryEnabled checks if a policy category is enabled
func (e *Engine) isCategoryEnabled(category string) bool {
	switch category {
	case "sql_injection":
		return e.config.Policies.SQLInjection
	case "xss":
		return e.config.Policies.XSS
	case "path_traversal":
		return e.config.Policies.PathTraversal
	case "command_injection":
		return e.config.Policies.CommandInjection
	default:
		return true // Enable other categories by default
	}
}

// getThreatLevel returns the threat level for a category
func (e *Engine) getThreatLevel(category string) database.ThreatLevel {
	switch category {
	case "sql_injection", "command_injection", "path_traversal":
		return database.ThreatLevelHigh
	case "xss", "file_inclusion":
		return database.ThreatLevelMedium
	case "rate_limit", "size_limit":
		return database.ThreatLevelMedium
	default:
		return database.ThreatLevelLow
	}
}

// calculateConfidence calculates confidence score for a policy match
func (e *Engine) calculateConfidence(policy *database.Policy, content map[string]string) *float64 {
	// Simple confidence calculation based on policy priority and pattern complexity
	baseConfidence := float64(policy.Priority) / 100.0
	if baseConfidence > 1.0 {
		baseConfidence = 1.0
	}

	// Adjust based on pattern complexity (longer patterns = higher confidence)
	patternComplexity := float64(len(policy.Pattern)) / 100.0
	if patternComplexity > 0.5 {
		patternComplexity = 0.5
	}

	confidence := baseConfidence + patternComplexity
	if confidence > 1.0 {
		confidence = 1.0
	}

	return &confidence
}
