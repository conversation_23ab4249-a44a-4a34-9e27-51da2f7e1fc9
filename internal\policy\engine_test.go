package policy

import (
	"testing"

	"github.com/waf-fav/internal/config"
	"github.com/waf-fav/internal/database"
)

func TestNewEngine(t *testing.T) {
	cfg := &config.Config{
		Policies: config.PoliciesConfig{
			SQLInjection: true,
			XSS:          true,
		},
	}

	// For now, we'll test with nil repository since we need a real database for full testing
	engine := NewEngine(cfg, nil)
	if engine == nil {
		t.Fatal("Expected engine to be created")
	}

	if engine.config != cfg {
		t.Error("Expected config to be set")
	}
}

func TestIsCategoryEnabled(t *testing.T) {
	cfg := &config.Config{
		Policies: config.PoliciesConfig{
			SQLInjection:     true,
			XSS:              false,
			PathTraversal:    true,
			CommandInjection: false,
		},
	}

	engine := NewEngine(cfg, nil)

	tests := []struct {
		category string
		expected bool
	}{
		{"sql_injection", true},
		{"xss", false},
		{"path_traversal", true},
		{"command_injection", false},
		{"unknown_category", true}, // Default to true for unknown categories
	}

	for _, tt := range tests {
		t.Run(tt.category, func(t *testing.T) {
			result := engine.isCategoryEnabled(tt.category)
			if result != tt.expected {
				t.Errorf("isCategoryEnabled(%s) = %v, want %v", tt.category, result, tt.expected)
			}
		})
	}
}

func TestGetThreatLevel(t *testing.T) {
	engine := NewEngine(&config.Config{}, nil)

	tests := []struct {
		category string
		expected database.ThreatLevel
	}{
		{"sql_injection", database.ThreatLevelHigh},
		{"command_injection", database.ThreatLevelHigh},
		{"path_traversal", database.ThreatLevelHigh},
		{"xss", database.ThreatLevelMedium},
		{"file_inclusion", database.ThreatLevelMedium},
		{"rate_limit", database.ThreatLevelMedium},
		{"size_limit", database.ThreatLevelMedium},
		{"unknown_category", database.ThreatLevelLow},
	}

	for _, tt := range tests {
		t.Run(tt.category, func(t *testing.T) {
			result := engine.getThreatLevel(tt.category)
			if result != tt.expected {
				t.Errorf("getThreatLevel(%s) = %v, want %v", tt.category, result, tt.expected)
			}
		})
	}
}
