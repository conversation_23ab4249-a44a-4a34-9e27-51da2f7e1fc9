package policy

import (
	"context"
	"fmt"
	"net"
	"time"

	"github.com/google/uuid"
	"github.com/waf-fav/internal/database"
)

// Manager handles policy management operations
type Manager struct {
	repository *database.Repository
}

// NewManager creates a new policy manager
func NewManager(repo *database.Repository) *Manager {
	return &Manager{
		repository: repo,
	}
}

// CreatePolicy creates a new security policy
func (m *Manager) CreatePolicy(ctx context.Context, policy *database.Policy) error {
	// Validate policy
	if err := m.validatePolicy(policy); err != nil {
		return fmt.Errorf("policy validation failed: %w", err)
	}

	// Set ID if not provided
	if policy.ID == uuid.Nil {
		policy.ID = uuid.New()
	}

	// Set timestamps
	now := time.Now()
	policy.CreatedAt = now
	policy.UpdatedAt = now

	// Create policy in database
	query := `
		INSERT INTO policies (id, name, category, pattern, action, enabled, priority, description, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
	`

	_, err := m.repository.ExecContext(ctx, query,
		policy.ID, policy.Name, policy.Category, policy.Pattern,
		policy.Action, policy.Enabled, policy.Priority,
		policy.Description, policy.CreatedAt, policy.UpdatedAt,
	)

	return err
}

// UpdatePolicy updates an existing policy
func (m *Manager) UpdatePolicy(ctx context.Context, policy *database.Policy) error {
	// Validate policy
	if err := m.validatePolicy(policy); err != nil {
		return fmt.Errorf("policy validation failed: %w", err)
	}

	// Update timestamp
	policy.UpdatedAt = time.Now()

	// Update policy in database
	query := `
		UPDATE policies 
		SET name = $2, category = $3, pattern = $4, action = $5, 
		    enabled = $6, priority = $7, description = $8, updated_at = $9
		WHERE id = $1
	`

	result, err := m.repository.ExecContext(ctx, query,
		policy.ID, policy.Name, policy.Category, policy.Pattern,
		policy.Action, policy.Enabled, policy.Priority,
		policy.Description, policy.UpdatedAt,
	)

	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return fmt.Errorf("policy with ID %s not found", policy.ID)
	}

	return nil
}

// DeletePolicy deletes a policy by ID
func (m *Manager) DeletePolicy(ctx context.Context, policyID uuid.UUID) error {
	query := `DELETE FROM policies WHERE id = $1`

	result, err := m.repository.ExecContext(ctx, query, policyID)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return fmt.Errorf("policy with ID %s not found", policyID)
	}

	return nil
}

// GetPolicy retrieves a policy by ID
func (m *Manager) GetPolicy(ctx context.Context, policyID uuid.UUID) (*database.Policy, error) {
	query := `
		SELECT id, name, category, pattern, action, enabled, priority, description, created_at, updated_at
		FROM policies WHERE id = $1
	`

	policy := &database.Policy{}
	err := m.repository.QueryRowContext(ctx, query, policyID).Scan(
		&policy.ID, &policy.Name, &policy.Category, &policy.Pattern,
		&policy.Action, &policy.Enabled, &policy.Priority,
		&policy.Description, &policy.CreatedAt, &policy.UpdatedAt,
	)

	if err != nil {
		return nil, err
	}

	return policy, nil
}

// ListPolicies retrieves all policies with optional filtering
func (m *Manager) ListPolicies(ctx context.Context, category string, enabled *bool) ([]*database.Policy, error) {
	query := `
		SELECT id, name, category, pattern, action, enabled, priority, description, created_at, updated_at
		FROM policies WHERE 1=1
	`
	args := []interface{}{}
	argIndex := 1

	if category != "" {
		query += fmt.Sprintf(" AND category = $%d", argIndex)
		args = append(args, category)
		argIndex++
	}

	if enabled != nil {
		query += fmt.Sprintf(" AND enabled = $%d", argIndex)
		args = append(args, *enabled)
		argIndex++
	}

	query += " ORDER BY priority DESC, category, name"

	rows, err := m.repository.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var policies []*database.Policy
	for rows.Next() {
		policy := &database.Policy{}
		err := rows.Scan(
			&policy.ID, &policy.Name, &policy.Category, &policy.Pattern,
			&policy.Action, &policy.Enabled, &policy.Priority,
			&policy.Description, &policy.CreatedAt, &policy.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		policies = append(policies, policy)
	}

	return policies, rows.Err()
}

// EnablePolicy enables a policy
func (m *Manager) EnablePolicy(ctx context.Context, policyID uuid.UUID) error {
	return m.setPolicyEnabled(ctx, policyID, true)
}

// DisablePolicy disables a policy
func (m *Manager) DisablePolicy(ctx context.Context, policyID uuid.UUID) error {
	return m.setPolicyEnabled(ctx, policyID, false)
}

// setPolicyEnabled sets the enabled status of a policy
func (m *Manager) setPolicyEnabled(ctx context.Context, policyID uuid.UUID, enabled bool) error {
	query := `UPDATE policies SET enabled = $1, updated_at = NOW() WHERE id = $2`

	result, err := m.repository.ExecContext(ctx, query, enabled, policyID)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return fmt.Errorf("policy with ID %s not found", policyID)
	}

	return nil
}

// BlockIP blocks an IP address
func (m *Manager) BlockIP(ctx context.Context, ip net.IP, reason string, duration *time.Duration) error {
	return m.repository.BlockIP(ctx, ip, reason, duration)
}

// UnblockIP removes an IP from the blocked list
func (m *Manager) UnblockIP(ctx context.Context, ip net.IP) error {
	query := `DELETE FROM blocked_ips WHERE ip_address = $1`

	result, err := m.repository.ExecContext(ctx, query, ip)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return fmt.Errorf("IP %s is not blocked", ip.String())
	}

	return nil
}

// ListBlockedIPs retrieves all blocked IPs
func (m *Manager) ListBlockedIPs(ctx context.Context) ([]*database.BlockedIP, error) {
	query := `
		SELECT id, ip_address, reason, blocked_until, created_at, updated_at
		FROM blocked_ips 
		WHERE blocked_until IS NULL OR blocked_until > NOW()
		ORDER BY created_at DESC
	`

	rows, err := m.repository.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var blockedIPs []*database.BlockedIP
	for rows.Next() {
		blockedIP := &database.BlockedIP{}
		err := rows.Scan(
			&blockedIP.ID, &blockedIP.IPAddress, &blockedIP.Reason,
			&blockedIP.BlockedUntil, &blockedIP.CreatedAt, &blockedIP.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		blockedIPs = append(blockedIPs, blockedIP)
	}

	return blockedIPs, rows.Err()
}

// CleanupExpiredBlocks removes expired IP blocks
func (m *Manager) CleanupExpiredBlocks(ctx context.Context) error {
	query := `DELETE FROM blocked_ips WHERE blocked_until IS NOT NULL AND blocked_until <= NOW()`

	_, err := m.repository.ExecContext(ctx, query)
	return err
}

// validatePolicy validates a policy before creation or update
func (m *Manager) validatePolicy(policy *database.Policy) error {
	if policy.Name == "" {
		return fmt.Errorf("policy name cannot be empty")
	}

	if policy.Category == "" {
		return fmt.Errorf("policy category cannot be empty")
	}

	if policy.Pattern == "" {
		return fmt.Errorf("policy pattern cannot be empty")
	}

	// Validate action
	validActions := map[database.PolicyAction]bool{
		database.PolicyActionBlock: true,
		database.PolicyActionLog:   true,
		database.PolicyActionAllow: true,
	}

	if !validActions[policy.Action] {
		return fmt.Errorf("invalid policy action: %s", policy.Action)
	}

	// Validate priority
	if policy.Priority < 0 || policy.Priority > 100 {
		return fmt.Errorf("policy priority must be between 0 and 100")
	}

	// Validate category
	validCategories := map[string]bool{
		"sql_injection":     true,
		"xss":               true,
		"path_traversal":    true,
		"command_injection": true,
		"file_inclusion":    true,
		"http_method":       true,
		"user_agent":        true,
		"header_injection":  true,
		"size_limit":        true,
		"encoding":          true,
		"protocol":          true,
		"rate_limit":        true,
		"custom":            true,
	}

	if !validCategories[policy.Category] {
		return fmt.Errorf("invalid policy category: %s", policy.Category)
	}

	return nil
}
