package proxy

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/waf-fav/internal/config"
)

// Proxy represents the reverse proxy
type Proxy struct {
	config       *config.Config
	reverseProxy *httputil.ReverseProxy
	upstreamURL  *url.URL
	client       *http.Client
}

// RequestInfo contains information about the proxied request
type RequestInfo struct {
	ID          uuid.UUID
	Method      string
	URL         string
	Headers     http.Header
	Body        string
	QueryParams map[string][]string
	ClientIP    net.IP
	UserAgent   string
	StartTime   time.Time
}

// ResponseInfo contains information about the response
type ResponseInfo struct {
	StatusCode   int
	Headers      http.Header
	Body         string
	ResponseTime time.Duration
}

// New creates a new reverse proxy instance
func New(cfg *config.Config) (*Proxy, error) {
	// Parse upstream URL
	upstreamURL, err := url.Parse(cfg.GetUpstreamURL())
	if err != nil {
		return nil, fmt.Errorf("failed to parse upstream URL: %w", err)
	}

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: cfg.Upstream.Timeout,
		Transport: &http.Transport{
			DialContext: (&net.Dialer{
				Timeout:   10 * time.Second,
				KeepAlive: 30 * time.Second,
			}).DialContext,
			MaxIdleConns:          100,
			IdleConnTimeout:       90 * time.Second,
			TLSHandshakeTimeout:   10 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
		},
	}

	proxy := &Proxy{
		config:      cfg,
		upstreamURL: upstreamURL,
		client:      client,
	}

	// Create reverse proxy with custom director
	proxy.reverseProxy = &httputil.ReverseProxy{
		Director:       proxy.director,
		ModifyResponse: proxy.modifyResponse,
		ErrorHandler:   proxy.errorHandler,
		Transport:      client.Transport,
	}

	return proxy, nil
}

// ServeHTTP implements the http.Handler interface
func (p *Proxy) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	p.reverseProxy.ServeHTTP(w, r)
}

// director modifies the request before sending it to the upstream
func (p *Proxy) director(req *http.Request) {
	// Set the target URL
	req.URL.Scheme = p.upstreamURL.Scheme
	req.URL.Host = p.upstreamURL.Host

	// Preserve the original path and query
	if p.upstreamURL.Path != "" && p.upstreamURL.Path != "/" {
		req.URL.Path = strings.TrimSuffix(p.upstreamURL.Path, "/") + req.URL.Path
	}

	// Set headers for upstream
	req.Header.Set("X-Forwarded-Proto", req.URL.Scheme)
	req.Header.Set("X-Forwarded-Host", req.Host)

	// Get client IP
	clientIP := p.getClientIP(req)
	req.Header.Set("X-Forwarded-For", clientIP.String())
	req.Header.Set("X-Real-IP", clientIP.String())

	// Add WAF identification header
	req.Header.Set("X-WAF-Protected", "true")

	// Remove hop-by-hop headers
	p.removeHopByHopHeaders(req.Header)
}

// modifyResponse modifies the response before sending it to the client
func (p *Proxy) modifyResponse(resp *http.Response) error {
	// Add security headers if enabled
	if p.config.SecurityHeaders.Enable {
		for key, value := range p.config.SecurityHeaders.Headers {
			resp.Header.Set(key, value)
		}
	}

	// Add WAF identification header
	resp.Header.Set("X-WAF-Protected", "true")

	// Remove hop-by-hop headers
	p.removeHopByHopHeaders(resp.Header)

	return nil
}

// errorHandler handles errors from the upstream server
func (p *Proxy) errorHandler(w http.ResponseWriter, r *http.Request, err error) {
	// Log the error (this would be handled by the logging system)
	
	// Set appropriate status code
	statusCode := http.StatusBadGateway
	if err == context.DeadlineExceeded {
		statusCode = http.StatusGatewayTimeout
	}

	// Add security headers even for error responses
	if p.config.SecurityHeaders.Enable {
		for key, value := range p.config.SecurityHeaders.Headers {
			w.Header().Set(key, value)
		}
	}

	w.Header().Set("X-WAF-Protected", "true")
	w.Header().Set("Content-Type", "application/json")

	w.WriteHeader(statusCode)
	
	errorResponse := fmt.Sprintf(`{
		"error": "upstream_error",
		"message": "The upstream server is currently unavailable",
		"status": %d,
		"timestamp": "%s"
	}`, statusCode, time.Now().UTC().Format(time.RFC3339))

	w.Write([]byte(errorResponse))
}

// ExtractRequestInfo extracts information from an HTTP request
func (p *Proxy) ExtractRequestInfo(r *http.Request) (*RequestInfo, error) {
	// Generate request ID
	requestID := uuid.New()

	// Get client IP
	clientIP := p.getClientIP(r)

	// Read and restore body
	body, err := p.readAndRestoreBody(r)
	if err != nil {
		return nil, fmt.Errorf("failed to read request body: %w", err)
	}

	// Extract query parameters
	queryParams := make(map[string][]string)
	for key, values := range r.URL.Query() {
		queryParams[key] = values
	}

	// Get user agent
	userAgent := r.Header.Get("User-Agent")

	return &RequestInfo{
		ID:          requestID,
		Method:      r.Method,
		URL:         r.URL.String(),
		Headers:     r.Header.Clone(),
		Body:        body,
		QueryParams: queryParams,
		ClientIP:    clientIP,
		UserAgent:   userAgent,
		StartTime:   time.Now(),
	}, nil
}

// getClientIP extracts the real client IP from the request
func (p *Proxy) getClientIP(r *http.Request) net.IP {
	// Check X-Forwarded-For header
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		ips := strings.Split(xff, ",")
		if len(ips) > 0 {
			ip := strings.TrimSpace(ips[0])
			if parsedIP := net.ParseIP(ip); parsedIP != nil {
				return parsedIP
			}
		}
	}

	// Check X-Real-IP header
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		if parsedIP := net.ParseIP(xri); parsedIP != nil {
			return parsedIP
		}
	}

	// Fall back to remote address
	host, _, err := net.SplitHostPort(r.RemoteAddr)
	if err != nil {
		host = r.RemoteAddr
	}

	if parsedIP := net.ParseIP(host); parsedIP != nil {
		return parsedIP
	}

	// Default to localhost if parsing fails
	return net.ParseIP("127.0.0.1")
}

// readAndRestoreBody reads the request body and restores it for further processing
func (p *Proxy) readAndRestoreBody(r *http.Request) (string, error) {
	if r.Body == nil {
		return "", nil
	}

	// Read the body
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		return "", err
	}

	// Restore the body for further processing
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	return string(bodyBytes), nil
}

// removeHopByHopHeaders removes hop-by-hop headers that shouldn't be forwarded
func (p *Proxy) removeHopByHopHeaders(headers http.Header) {
	hopByHopHeaders := []string{
		"Connection",
		"Keep-Alive",
		"Proxy-Authenticate",
		"Proxy-Authorization",
		"Te",
		"Trailers",
		"Transfer-Encoding",
		"Upgrade",
	}

	for _, header := range hopByHopHeaders {
		headers.Del(header)
	}
}

// CreateBlockedResponse creates a response for blocked requests
func (p *Proxy) CreateBlockedResponse(w http.ResponseWriter, reason string) {
	// Add security headers
	if p.config.SecurityHeaders.Enable {
		for key, value := range p.config.SecurityHeaders.Headers {
			w.Header().Set(key, value)
		}
	}

	w.Header().Set("X-WAF-Protected", "true")
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusForbidden)

	blockedResponse := fmt.Sprintf(`{
		"error": "request_blocked",
		"message": "Request blocked by WAF",
		"reason": "%s",
		"status": 403,
		"timestamp": "%s"
	}`, reason, time.Now().UTC().Format(time.RFC3339))

	w.Write([]byte(blockedResponse))
}

// CreateHealthResponse creates a health check response
func (p *Proxy) CreateHealthResponse(w http.ResponseWriter) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("X-WAF-Protected", "true")
	w.WriteHeader(http.StatusOK)

	healthResponse := fmt.Sprintf(`{
		"status": "healthy",
		"service": "waf",
		"mode": "%s",
		"upstream": "%s",
		"timestamp": "%s"
	}`, p.config.Mode, p.config.GetUpstreamURL(), time.Now().UTC().Format(time.RFC3339))

	w.Write([]byte(healthResponse))
}

// IsHealthCheck checks if the request is a health check
func (p *Proxy) IsHealthCheck(r *http.Request) bool {
	return r.URL.Path == "/health" && r.Method == "GET"
}
