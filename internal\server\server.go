package server

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/waf-fav/internal/config"
	"github.com/waf-fav/internal/database"
	"github.com/waf-fav/internal/logging"
	"github.com/waf-fav/internal/monitoring"
	"github.com/waf-fav/internal/proxy"
	"github.com/waf-fav/internal/waf"
)

// Server represents the WAF server
type Server struct {
	config     *config.Config
	httpServer *http.Server
	db         *database.DB
	logger     *logging.Logger
	metrics    *monitoring.Metrics
	wafEngine  *waf.Engine
	wafHandler *waf.Handler
}

// New creates a new server instance
func New(cfg *config.Config) (*Server, error) {
	// Initialize logger
	log.Println("1")
	logger, err := logging.New(&cfg.Logging)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize logger: %w", err)
	}

	// Initialize database
	log.Println("2")
	db, err := database.New(&cfg.Database)
	if err != nil {
		logger.Error("Failed to connect to database", err, nil)
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	logger.DatabaseConnection("connected", cfg.Database.Host, cfg.Database.Name, nil)

	log.Println("3")
	// Initialize repository
	repository := database.NewRepository(db)

	// Initialize metrics
	metrics := monitoring.New(repository)

	log.Println("4")

	// Initialize WAF engine
	wafEngine, err := waf.New(cfg, repository)
	if err != nil {
		logger.Error("Failed to create WAF engine", err, nil)
		return nil, fmt.Errorf("failed to create WAF engine: %w", err)
	}

	log.Println("5")

	// Initialize WAF handler
	wafHandler := waf.NewHandler(wafEngine)

	log.Println("6")

	// Create HTTP server
	mux := http.NewServeMux()

	// Setup routes
	server := &Server{
		config:     cfg,
		db:         db,
		logger:     logger,
		metrics:    metrics,
		wafEngine:  wafEngine,
		wafHandler: wafHandler,
	}

	server.setupRoutes(mux)

	log.Println("7")

	httpServer := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler:      server.createMiddlewareChain(mux),
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	server.httpServer = httpServer

	return server, nil
}

// Start starts the server
func (s *Server) Start() error {
	// Initialize WAF engine
	ctx := context.Background()
	if err := s.wafEngine.Initialize(ctx); err != nil {
		s.logger.Error("Failed to initialize WAF engine", err, nil)
		return fmt.Errorf("failed to initialize WAF engine: %w", err)
	}

	s.logger.Startup("1.0.0", s.config.Mode, s.config.Server.Port)

	// Start HTTP server
	s.logger.Info("Starting WAF server", logging.Fields{
		"address": s.httpServer.Addr,
		"mode":    s.config.Mode,
	})

	if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		s.logger.Error("Server failed to start", err, nil)
		return fmt.Errorf("server failed to start: %w", err)
	}

	return nil
}

// Shutdown gracefully shuts down the server
func (s *Server) Shutdown() error {
	s.logger.Shutdown("graceful shutdown requested")

	// Create shutdown context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown HTTP server
	if err := s.httpServer.Shutdown(ctx); err != nil {
		s.logger.Error("Failed to shutdown HTTP server", err, nil)
		return fmt.Errorf("failed to shutdown HTTP server: %w", err)
	}

	// Close database connection
	if err := s.db.Close(); err != nil {
		s.logger.Error("Failed to close database connection", err, nil)
		return fmt.Errorf("failed to close database connection: %w", err)
	}

	s.logger.Info("Server shutdown completed", nil)
	return nil
}

// setupRoutes sets up HTTP routes
func (s *Server) setupRoutes(mux *http.ServeMux) {
	// Health check endpoint
	mux.HandleFunc("/health", s.wafHandler.HealthHandler)

	// Management API endpoints
	mux.HandleFunc("/api/statistics", s.wafHandler.StatisticsHandler)
	mux.HandleFunc("/api/policies", s.wafHandler.PoliciesHandler)
	mux.HandleFunc("/api/policies/", s.wafHandler.PolicyHandler)
	mux.HandleFunc("/api/policies/reload", s.wafHandler.ReloadPoliciesHandler)
	mux.HandleFunc("/api/blocked-ips", s.wafHandler.BlockedIPsHandler)

	// Metrics endpoint
	mux.HandleFunc("/api/metrics", s.metricsHandler)

	// Default handler for all other requests (WAF processing)
	mux.HandleFunc("/", s.wafEngine.ProcessRequest)
}

// createMiddlewareChain creates the middleware chain
func (s *Server) createMiddlewareChain(handler http.Handler) http.Handler {
	// Create middleware chain
	chain := proxy.NewMiddlewareChain(
		proxy.RecoveryMiddleware(),
		proxy.RequestIDMiddleware(),
		s.loggingMiddleware(),
		s.metricsMiddleware(),
		proxy.SecurityHeadersMiddleware(s.config.SecurityHeaders.Headers),
	)

	return chain.Then(handler)
}

// loggingMiddleware creates a logging middleware
func (s *Server) loggingMiddleware() proxy.Middleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()

			// Create response writer wrapper
			wrapper := &responseWriterWrapper{
				ResponseWriter: w,
				statusCode:     http.StatusOK,
			}

			// Process request
			next.ServeHTTP(wrapper, r)

			// Log request
			duration := time.Since(start)
			clientIP := s.getClientIP(r)
			requestID := r.Header.Get("X-Request-ID")

			s.logger.Request(
				r.Method,
				r.URL.String(),
				clientIP,
				r.Header.Get("User-Agent"),
				wrapper.statusCode,
				duration,
				requestID,
			)
		})
	}
}

// metricsMiddleware creates a metrics middleware
func (s *Server) metricsMiddleware() proxy.Middleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()

			// Create response writer wrapper
			wrapper := &responseWriterWrapper{
				ResponseWriter: w,
				statusCode:     http.StatusOK,
			}

			// Process request
			next.ServeHTTP(wrapper, r)

			// Record metrics
			duration := time.Since(start)
			clientIP := s.getClientIP(r)
			status := "allowed"
			if wrapper.statusCode == http.StatusForbidden {
				status = "blocked"
			}

			s.metrics.RecordRequest(
				r.Method,
				status,
				clientIP,
				r.Header.Get("User-Agent"),
				duration,
			)
		})
	}
}

// metricsHandler handles metrics requests
func (s *Server) metricsHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	summary := s.metrics.GetSummary()

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(summary); err != nil {
		s.logger.Error("Failed to encode metrics", err, nil)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
}

// getClientIP extracts the client IP from the request
func (s *Server) getClientIP(r *http.Request) string {
	// Check X-Forwarded-For header
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		return xff
	}

	// Check X-Real-IP header
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return xri
	}

	// Fall back to remote address
	return r.RemoteAddr
}

// responseWriterWrapper wraps http.ResponseWriter to capture status code
type responseWriterWrapper struct {
	http.ResponseWriter
	statusCode int
}

// WriteHeader captures the status code
func (w *responseWriterWrapper) WriteHeader(statusCode int) {
	w.statusCode = statusCode
	w.ResponseWriter.WriteHeader(statusCode)
}

// Write ensures WriteHeader is called with 200 if not already called
func (w *responseWriterWrapper) Write(data []byte) (int, error) {
	if w.statusCode == 0 {
		w.statusCode = http.StatusOK
	}
	return w.ResponseWriter.Write(data)
}
