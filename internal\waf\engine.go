package waf

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/google/uuid"
	"github.com/waf-fav/internal/config"
	"github.com/waf-fav/internal/database"
	"github.com/waf-fav/internal/policy"
	"github.com/waf-fav/internal/proxy"
)

// Engine represents the main WAF engine
type Engine struct {
	config        *config.Config
	repository    *database.Repository
	policyEngine  *policy.Engine
	proxy         *proxy.Proxy
	policyManager *policy.Manager
}

// Decision represents a WAF decision
type Decision struct {
	Action          string                    `json:"action"`
	Allowed         bool                      `json:"allowed"`
	Blocked         bool                      `json:"blocked"`
	Reason          string                    `json:"reason"`
	ThreatLevel     string                    `json:"threat_level"`
	ConfidenceScore float64                   `json:"confidence_score"`
	MatchedPolicies []*database.Policy        `json:"matched_policies,omitempty"`
	SecurityEvents  []*database.SecurityEvent `json:"security_events,omitempty"`
	RequestID       uuid.UUID                 `json:"request_id"`
	ProcessingTime  time.Duration             `json:"processing_time"`
}

// New creates a new WAF engine
func New(cfg *config.Config, repo *database.Repository) (*Engine, error) {
	// Create policy engine
	policyEngine := policy.NewEngine(cfg, repo)

	// Create proxy
	proxyInstance, err := proxy.New(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create proxy: %w", err)
	}

	// Create policy manager
	policyManager := policy.NewManager(repo)

	engine := &Engine{
		config:        cfg,
		repository:    repo,
		policyEngine:  policyEngine,
		proxy:         proxyInstance,
		policyManager: policyManager,
	}

	return engine, nil
}

// Initialize initializes the WAF engine
func (e *Engine) Initialize(ctx context.Context) error {
	// Load policies into the policy engine
	if err := e.policyEngine.LoadPolicies(ctx); err != nil {
		return fmt.Errorf("failed to load policies: %w", err)
	}

	return nil
}

// ProcessRequest processes an incoming HTTP request through the WAF
func (e *Engine) ProcessRequest(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()

	// Handle health checks
	if e.proxy.IsHealthCheck(r) {
		e.proxy.CreateHealthResponse(w)
		return
	}

	// Extract request information
	requestInfo, err := e.proxy.ExtractRequestInfo(r)
	if err != nil {
		e.handleError(w, "Failed to extract request information", http.StatusInternalServerError)
		return
	}

	// Create request context for policy evaluation
	reqCtx := &policy.RequestContext{
		Method:      requestInfo.Method,
		URL:         requestInfo.URL,
		Headers:     requestInfo.Headers,
		Body:        requestInfo.Body,
		QueryParams: requestInfo.QueryParams,
		ClientIP:    requestInfo.ClientIP,
		UserAgent:   requestInfo.UserAgent,
		RequestID:   requestInfo.ID,
	}

	// Evaluate request against policies
	ctx := context.Background()
	result, err := e.policyEngine.Evaluate(ctx, reqCtx)
	if err != nil {
		e.handleError(w, "Failed to evaluate request", http.StatusInternalServerError)
		return
	}

	// Create decision
	decision := &Decision{
		RequestID:       requestInfo.ID,
		ProcessingTime:  time.Since(startTime),
		ThreatLevel:     string(result.ThreatLevel),
		ConfidenceScore: result.ConfidenceScore,
		MatchedPolicies: result.BlockedBy,
		SecurityEvents:  result.SecurityEvents,
	}

	// Determine action based on mode and evaluation result
	if result.ShouldBlock && e.config.IsBlockMode() {
		decision.Action = "blocked"
		decision.Blocked = true
		decision.Allowed = false
		decision.Reason = e.buildBlockReason(result)

		// Log the request as blocked
		e.logRequest(ctx, requestInfo, database.RequestStatusBlocked, 0, 0)

		// Log security events
		e.logSecurityEvents(ctx, result.SecurityEvents)

		// Return blocked response
		e.proxy.CreateBlockedResponse(w, decision.Reason)
		return
	}

	// Request is allowed - proxy to upstream
	decision.Action = "allowed"
	decision.Allowed = true
	decision.Blocked = false

	if len(result.SecurityEvents) > 0 {
		decision.Reason = "Request allowed but security events detected"
	} else {
		decision.Reason = "Request allowed"
	}

	// Log the request as allowed
	e.logRequest(ctx, requestInfo, database.RequestStatusAllowed, 0, 0)

	// Log security events if any
	if len(result.SecurityEvents) > 0 {
		e.logSecurityEvents(ctx, result.SecurityEvents)
	}

	// Add decision information to request headers for upstream
	decisionJSON, _ := json.Marshal(decision)
	r.Header.Set("X-WAF-Decision", string(decisionJSON))
	r.Header.Set("X-WAF-Request-ID", requestInfo.ID.String())

	// Proxy the request
	e.proxy.ServeHTTP(w, r)
}

// logRequest logs the request to the database
func (e *Engine) logRequest(ctx context.Context, requestInfo *proxy.RequestInfo, status database.RequestStatus, responseStatus, responseTime int) {
	// Convert headers to JSON
	headersJSON, _ := json.Marshal(requestInfo.Headers)

	// Convert query params to JSON
	queryParamsJSON, _ := json.Marshal(requestInfo.QueryParams)

	// Create request record
	request := &database.Request{
		ID:          requestInfo.ID,
		Timestamp:   requestInfo.StartTime,
		ClientIP:    requestInfo.ClientIP,
		Method:      requestInfo.Method,
		URL:         requestInfo.URL,
		UserAgent:   &requestInfo.UserAgent,
		Headers:     headersJSON,
		Body:        &requestInfo.Body,
		QueryParams: queryParamsJSON,
		Status:      status,
	}

	if responseStatus > 0 {
		request.ResponseStatus = &responseStatus
	}

	if responseTime > 0 {
		request.ResponseTimeMs = &responseTime
	}

	// Save to database
	if err := e.repository.CreateRequest(ctx, request); err != nil {
		// Log error but don't fail the request
		// logger.Error("Failed to log request", "error", err)
	}
}

// logSecurityEvents logs security events to the database
func (e *Engine) logSecurityEvents(ctx context.Context, events []*database.SecurityEvent) {
	for _, event := range events {
		if err := e.repository.CreateSecurityEvent(ctx, event); err != nil {
			// Log error but continue with other events
			// logger.Error("Failed to log security event", "error", err, "event_id", event.ID)
		}
	}
}

// buildBlockReason builds a human-readable reason for blocking
func (e *Engine) buildBlockReason(result *policy.EvaluationResult) string {
	if len(result.BlockedBy) == 0 {
		return "Request blocked by WAF"
	}

	if len(result.BlockedBy) == 1 {
		return fmt.Sprintf("Request blocked by policy: %s", result.BlockedBy[0].Name)
	}

	return fmt.Sprintf("Request blocked by %d policies", len(result.BlockedBy))
}

// handleError handles internal errors
func (e *Engine) handleError(w http.ResponseWriter, message string, statusCode int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	errorResponse := fmt.Sprintf(`{
		"error": "waf_error",
		"message": "%s",
		"status": %d,
		"timestamp": "%s"
	}`, message, statusCode, time.Now().UTC().Format(time.RFC3339))

	w.Write([]byte(errorResponse))
}

// GetStatistics returns WAF statistics
func (e *Engine) GetStatistics(ctx context.Context, days int) (*Statistics, error) {
	// This would query the database for statistics
	// For now, return a placeholder
	return &Statistics{
		TotalRequests:   0,
		BlockedRequests: 0,
		AllowedRequests: 0,
		TopThreats:      []ThreatStat{},
		TopIPs:          []IPStat{},
	}, nil
}

// ReloadPolicies reloads policies from the database
func (e *Engine) ReloadPolicies(ctx context.Context) error {
	return e.policyEngine.LoadPolicies(ctx)
}

// Statistics represents WAF statistics
type Statistics struct {
	TotalRequests   int64        `json:"total_requests"`
	BlockedRequests int64        `json:"blocked_requests"`
	AllowedRequests int64        `json:"allowed_requests"`
	TopThreats      []ThreatStat `json:"top_threats"`
	TopIPs          []IPStat     `json:"top_ips"`
}

// ThreatStat represents threat statistics
type ThreatStat struct {
	Type  string `json:"type"`
	Count int64  `json:"count"`
}

// IPStat represents IP statistics
type IPStat struct {
	IP    string `json:"ip"`
	Count int64  `json:"count"`
}

// Health checks the health of the WAF engine
func (e *Engine) Health(ctx context.Context) error {
	// Check database connection
	if err := e.repository.Health(); err != nil {
		return fmt.Errorf("database health check failed: %w", err)
	}

	// Check if policies are loaded - we'll skip this check for now
	// since policies field is not exported
	// if len(e.policyEngine.policies) == 0 {
	//     return fmt.Errorf("no policies loaded")
	// }

	return nil
}
