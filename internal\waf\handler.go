package waf

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/waf-fav/internal/database"
)

// Handler provides HTTP handlers for WAF management
type Handler struct {
	engine *Engine
}

// NewHandler creates a new WAF handler
func NewHandler(engine *Engine) *Handler {
	return &Handler{
		engine: engine,
	}
}

// HealthHandler handles health check requests
func (h *Handler) HealthHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	ctx := r.Context()
	if err := h.engine.Health(ctx); err != nil {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusServiceUnavailable)

		response := map[string]interface{}{
			"status":    "unhealthy",
			"error":     err.<PERSON>rror(),
			"timestamp": time.Now().UTC().Format(time.RFC3339),
		}

		json.NewEncoder(w).Encode(response)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	response := map[string]interface{}{
		"status":    "healthy",
		"service":   "waf",
		"mode":      h.engine.config.Mode,
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	json.NewEncoder(w).Encode(response)
}

// StatisticsHandler handles statistics requests
func (h *Handler) StatisticsHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Parse days parameter
	daysStr := r.URL.Query().Get("days")
	days := 7 // default to 7 days
	if daysStr != "" {
		if parsedDays, err := strconv.Atoi(daysStr); err == nil && parsedDays > 0 {
			days = parsedDays
		}
	}

	ctx := r.Context()
	stats, err := h.engine.GetStatistics(ctx, days)
	if err != nil {
		http.Error(w, "Failed to get statistics", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(stats)
}

// PoliciesHandler handles policy management requests
func (h *Handler) PoliciesHandler(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodGet:
		h.listPolicies(w, r)
	case http.MethodPost:
		h.createPolicy(w, r)
	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}

// PolicyHandler handles individual policy requests
func (h *Handler) PolicyHandler(w http.ResponseWriter, r *http.Request) {
	// Extract policy ID from URL path
	policyIDStr := r.URL.Path[len("/api/policies/"):]
	if policyIDStr == "" {
		http.Error(w, "Policy ID required", http.StatusBadRequest)
		return
	}

	policyID, err := uuid.Parse(policyIDStr)
	if err != nil {
		http.Error(w, "Invalid policy ID", http.StatusBadRequest)
		return
	}

	switch r.Method {
	case http.MethodGet:
		h.getPolicy(w, r, policyID)
	case http.MethodPut:
		h.updatePolicy(w, r, policyID)
	case http.MethodDelete:
		h.deletePolicy(w, r, policyID)
	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}

// ReloadPoliciesHandler handles policy reload requests
func (h *Handler) ReloadPoliciesHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	ctx := r.Context()
	if err := h.engine.ReloadPolicies(ctx); err != nil {
		http.Error(w, "Failed to reload policies", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	response := map[string]interface{}{
		"message":   "Policies reloaded successfully",
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	json.NewEncoder(w).Encode(response)
}

// listPolicies lists all policies
func (h *Handler) listPolicies(w http.ResponseWriter, r *http.Request) {
	category := r.URL.Query().Get("category")
	enabledStr := r.URL.Query().Get("enabled")

	var enabled *bool
	if enabledStr != "" {
		if enabledStr == "true" {
			enabled = &[]bool{true}[0]
		} else if enabledStr == "false" {
			enabled = &[]bool{false}[0]
		}
	}

	ctx := r.Context()
	policies, err := h.engine.policyManager.ListPolicies(ctx, category, enabled)
	if err != nil {
		http.Error(w, "Failed to list policies", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(policies)
}

// createPolicy creates a new policy
func (h *Handler) createPolicy(w http.ResponseWriter, r *http.Request) {
	var policy struct {
		Name        string `json:"name"`
		Category    string `json:"category"`
		Pattern     string `json:"pattern"`
		Action      string `json:"action"`
		Enabled     bool   `json:"enabled"`
		Priority    int    `json:"priority"`
		Description string `json:"description"`
	}

	if err := json.NewDecoder(r.Body).Decode(&policy); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Convert to database policy
	dbPolicy := &database.Policy{
		ID:          uuid.New(),
		Name:        policy.Name,
		Category:    policy.Category,
		Pattern:     policy.Pattern,
		Action:      database.PolicyAction(policy.Action),
		Enabled:     policy.Enabled,
		Priority:    policy.Priority,
		Description: &policy.Description,
	}

	ctx := r.Context()
	if err := h.engine.policyManager.CreatePolicy(ctx, dbPolicy); err != nil {
		http.Error(w, "Failed to create policy", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(dbPolicy)
}

// getPolicy retrieves a specific policy
func (h *Handler) getPolicy(w http.ResponseWriter, r *http.Request, policyID uuid.UUID) {
	ctx := r.Context()
	policy, err := h.engine.policyManager.GetPolicy(ctx, policyID)
	if err != nil {
		http.Error(w, "Policy not found", http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(policy)
}

// updatePolicy updates a specific policy
func (h *Handler) updatePolicy(w http.ResponseWriter, r *http.Request, policyID uuid.UUID) {
	var updateData struct {
		Name        string `json:"name"`
		Category    string `json:"category"`
		Pattern     string `json:"pattern"`
		Action      string `json:"action"`
		Enabled     bool   `json:"enabled"`
		Priority    int    `json:"priority"`
		Description string `json:"description"`
	}

	if err := json.NewDecoder(r.Body).Decode(&updateData); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Get existing policy
	ctx := r.Context()
	policy, err := h.engine.policyManager.GetPolicy(ctx, policyID)
	if err != nil {
		http.Error(w, "Policy not found", http.StatusNotFound)
		return
	}

	// Update fields
	policy.Name = updateData.Name
	policy.Category = updateData.Category
	policy.Pattern = updateData.Pattern
	policy.Action = database.PolicyAction(updateData.Action)
	policy.Enabled = updateData.Enabled
	policy.Priority = updateData.Priority
	policy.Description = &updateData.Description

	if err := h.engine.policyManager.UpdatePolicy(ctx, policy); err != nil {
		http.Error(w, "Failed to update policy", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(policy)
}

// deletePolicy deletes a specific policy
func (h *Handler) deletePolicy(w http.ResponseWriter, r *http.Request, policyID uuid.UUID) {
	ctx := r.Context()
	if err := h.engine.policyManager.DeletePolicy(ctx, policyID); err != nil {
		http.Error(w, "Failed to delete policy", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}

// BlockedIPsHandler handles blocked IP management
func (h *Handler) BlockedIPsHandler(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodGet:
		h.listBlockedIPs(w, r)
	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}

// listBlockedIPs lists all blocked IPs
func (h *Handler) listBlockedIPs(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	blockedIPs, err := h.engine.policyManager.ListBlockedIPs(ctx)
	if err != nil {
		http.Error(w, "Failed to list blocked IPs", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(blockedIPs)
}
