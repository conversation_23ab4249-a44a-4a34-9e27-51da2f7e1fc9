-- WAF Database Initial Schema
-- This file contains the initial database schema for the WAF application

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>reate enum types
CREATE TYPE request_status AS ENUM ('allowed', 'blocked', 'logged');
CREATE TYPE policy_action AS ENUM ('block', 'log', 'allow');
CREATE TYPE threat_level AS ENUM ('low', 'medium', 'high', 'critical');

-- Requests table - stores all incoming requests
CREATE TABLE requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    client_ip INET NOT NULL,
    method VARCHAR(10) NOT NULL,
    url TEXT NOT NULL,
    user_agent TEXT,
    headers JSONB,
    body TEXT,
    query_params JSONB,
    status request_status NOT NULL,
    response_status INTEGER,
    response_time_ms INTEGER,
    upstream_host VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Security events table - stores detected security threats
CREATE TABLE security_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    request_id UUID REFERENCES requests(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL, -- sql_injection, xss, path_traversal, etc.
    threat_level threat_level NOT NULL,
    description TEXT NOT NULL,
    matched_pattern TEXT,
    policy_id UUID,
    action_taken policy_action NOT NULL,
    confidence_score DECIMAL(3,2), -- 0.00 to 1.00
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Policies table - stores WAF security policies
CREATE TABLE policies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    category VARCHAR(50) NOT NULL, -- sql_injection, xss, rate_limit, etc.
    pattern TEXT NOT NULL,
    action policy_action NOT NULL DEFAULT 'log',
    enabled BOOLEAN NOT NULL DEFAULT true,
    priority INTEGER NOT NULL DEFAULT 100,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Blocked IPs table - stores temporarily or permanently blocked IPs
CREATE TABLE blocked_ips (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ip_address INET NOT NULL UNIQUE,
    reason TEXT NOT NULL,
    blocked_until TIMESTAMP WITH TIME ZONE, -- NULL for permanent blocks
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Rate limiting table - tracks request rates per IP
CREATE TABLE rate_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ip_address INET NOT NULL,
    window_start TIMESTAMP WITH TIME ZONE NOT NULL,
    request_count INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(ip_address, window_start)
);

-- WAF statistics table - stores aggregated statistics
CREATE TABLE waf_statistics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL UNIQUE,
    total_requests BIGINT NOT NULL DEFAULT 0,
    blocked_requests BIGINT NOT NULL DEFAULT 0,
    allowed_requests BIGINT NOT NULL DEFAULT 0,
    unique_ips INTEGER NOT NULL DEFAULT 0,
    top_threat_types JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_requests_timestamp ON requests(timestamp);
CREATE INDEX idx_requests_client_ip ON requests(client_ip);
CREATE INDEX idx_requests_status ON requests(status);
CREATE INDEX idx_requests_method ON requests(method);

CREATE INDEX idx_security_events_request_id ON security_events(request_id);
CREATE INDEX idx_security_events_event_type ON security_events(event_type);
CREATE INDEX idx_security_events_threat_level ON security_events(threat_level);
CREATE INDEX idx_security_events_created_at ON security_events(created_at);

CREATE INDEX idx_policies_category ON policies(category);
CREATE INDEX idx_policies_enabled ON policies(enabled);
CREATE INDEX idx_policies_priority ON policies(priority);

CREATE INDEX idx_blocked_ips_ip_address ON blocked_ips(ip_address);
CREATE INDEX idx_blocked_ips_blocked_until ON blocked_ips(blocked_until);

CREATE INDEX idx_rate_limits_ip_address ON rate_limits(ip_address);
CREATE INDEX idx_rate_limits_window_start ON rate_limits(window_start);

CREATE INDEX idx_waf_statistics_date ON waf_statistics(date);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_policies_updated_at BEFORE UPDATE ON policies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blocked_ips_updated_at BEFORE UPDATE ON blocked_ips
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rate_limits_updated_at BEFORE UPDATE ON rate_limits
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_waf_statistics_updated_at BEFORE UPDATE ON waf_statistics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
