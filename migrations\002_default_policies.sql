-- Default WAF Policies
-- This file contains default security policies for the WAF

-- SQL Injection Policies
INSERT INTO policies (name, category, pattern, action, priority, description) VALUES
('Basic SQL Injection - Union', 'sql_injection', '(?i)(union\s+(all\s+)?select)', 'block', 90, 'Detects UNION-based SQL injection attempts'),
('SQL Injection - OR 1=1', 'sql_injection', '(?i)(or\s+[''"]?1[''"]?\s*=\s*[''"]?1)', 'block', 90, 'Detects OR 1=1 SQL injection patterns'),
('SQL Injection - Comments', 'sql_injection', '(?i)(--|\#|/\*|\*/)', 'log', 70, 'Detects SQL comment injection attempts'),
('SQL Injection - Semicolon', 'sql_injection', '(?i)(;\s*(drop|delete|insert|update|create|alter))', 'block', 95, 'Detects SQL injection with semicolon separators'),
('SQL Injection - Information Schema', 'sql_injection', '(?i)(information_schema|sysobjects|syscolumns)', 'block', 85, 'Detects information schema enumeration attempts');

-- XSS (Cross-Site Scripting) Policies
INSERT INTO policies (name, category, pattern, action, priority, description) VALUES
('XSS - Script Tags', 'xss', '(?i)<script[^>]*>.*?</script>', 'block', 90, 'Detects script tag XSS attempts'),
('XSS - JavaScript Events', 'xss', '(?i)(on\w+\s*=)', 'block', 85, 'Detects JavaScript event handler XSS'),
('XSS - JavaScript Protocol', 'xss', '(?i)(javascript\s*:)', 'block', 85, 'Detects javascript: protocol XSS'),
('XSS - Data URLs', 'xss', '(?i)(data\s*:\s*text/html)', 'log', 70, 'Detects data URL XSS attempts'),
('XSS - Expression', 'xss', '(?i)(expression\s*\()', 'block', 80, 'Detects CSS expression XSS');

-- Path Traversal Policies
INSERT INTO policies (name, category, pattern, action, priority, description) VALUES
('Path Traversal - Dot Dot Slash', 'path_traversal', '(\.\./|\.\.\\)', 'block', 90, 'Detects directory traversal attempts'),
('Path Traversal - Encoded', 'path_traversal', '(%2e%2e%2f|%2e%2e%5c|%252e%252e%252f)', 'block', 90, 'Detects URL-encoded directory traversal'),
('Path Traversal - System Files', 'path_traversal', '(?i)(etc/passwd|boot\.ini|windows/system32)', 'block', 95, 'Detects attempts to access system files'),
('Path Traversal - Null Bytes', 'path_traversal', '(%00|\\x00)', 'block', 85, 'Detects null byte injection for path traversal');

-- Command Injection Policies
INSERT INTO policies (name, category, pattern, action, priority, description) VALUES
('Command Injection - Pipe', 'command_injection', '(\||\|\|)', 'block', 90, 'Detects pipe-based command injection'),
('Command Injection - Semicolon', 'command_injection', '(;\s*(cat|ls|dir|type|echo|whoami|id|uname))', 'block', 95, 'Detects semicolon command injection'),
('Command Injection - Backticks', 'command_injection', '(`[^`]*`)', 'block', 90, 'Detects backtick command execution'),
('Command Injection - Dollar Parentheses', 'command_injection', '(\$\([^)]*\))', 'block', 90, 'Detects $() command substitution'),
('Command Injection - System Commands', 'command_injection', '(?i)(wget|curl|nc|netcat|telnet|ssh)', 'log', 75, 'Detects system command usage');

-- File Inclusion Policies
INSERT INTO policies (name, category, pattern, action, priority, description) VALUES
('LFI - PHP Wrappers', 'file_inclusion', '(?i)(php://|file://|data://)', 'block', 90, 'Detects PHP wrapper usage for LFI'),
('LFI - Common Files', 'file_inclusion', '(?i)(wp-config\.php|\.htaccess|web\.config)', 'block', 85, 'Detects attempts to include sensitive files'),
('RFI - Remote URLs', 'file_inclusion', '(?i)(https?://|ftp://)', 'log', 70, 'Detects potential remote file inclusion');

-- HTTP Method Policies
INSERT INTO policies (name, category, pattern, action, priority, description) VALUES
('Dangerous HTTP Methods', 'http_method', '^(TRACE|TRACK|DEBUG|OPTIONS)$', 'block', 80, 'Blocks potentially dangerous HTTP methods'),
('PUT/DELETE Methods', 'http_method', '^(PUT|DELETE)$', 'log', 60, 'Logs PUT and DELETE method usage');

-- User Agent Policies
INSERT INTO policies (name, category, pattern, action, priority, description) VALUES
('Suspicious User Agents', 'user_agent', '(?i)(sqlmap|nikto|nmap|masscan|zap|burp)', 'block', 85, 'Blocks known security scanning tools'),
('Empty User Agent', 'user_agent', '^$', 'log', 50, 'Logs requests with empty user agent'),
('Bot User Agents', 'user_agent', '(?i)(bot|crawler|spider|scraper)', 'log', 30, 'Logs bot traffic');

-- Header Injection Policies
INSERT INTO policies (name, category, pattern, action, priority, description) VALUES
('CRLF Injection', 'header_injection', '(\r\n|\n|\r)', 'block', 90, 'Detects CRLF injection attempts'),
('Header Injection', 'header_injection', '(?i)(content-length|content-type|location):', 'log', 70, 'Detects potential header injection');

-- Size Limit Policies
INSERT INTO policies (name, category, pattern, action, priority, description) VALUES
('Large Request Body', 'size_limit', '.{10000,}', 'log', 60, 'Logs requests with large bodies'),
('Long URL', 'size_limit', '^.{2000,}$', 'log', 60, 'Logs requests with very long URLs');

-- Encoding Policies
INSERT INTO policies (name, category, pattern, action, priority, description) VALUES
('Double URL Encoding', 'encoding', '(%25[0-9a-fA-F]{2})', 'log', 70, 'Detects double URL encoding attempts'),
('Unicode Encoding', 'encoding', '(%u[0-9a-fA-F]{4})', 'log', 65, 'Detects Unicode encoding attempts');

-- Protocol Policies
INSERT INTO policies (name, category, pattern, action, priority, description) VALUES
('Non-HTTP Protocols', 'protocol', '(?i)(ldap://|gopher://|dict://|sftp://)', 'block', 80, 'Blocks non-HTTP protocol usage'),
('File Protocol', 'protocol', '(?i)(file://)', 'block', 85, 'Blocks file protocol usage');
