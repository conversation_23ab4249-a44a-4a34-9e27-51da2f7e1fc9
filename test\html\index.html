<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WAF Test Upstream Server</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .endpoint {
            background-color: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .method {
            font-weight: bold;
            color: #007bff;
        }
        .vulnerable {
            border-left-color: #dc3545;
        }
        .vulnerable .method {
            color: #dc3545;
        }
        code {
            background-color: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ WAF Test Upstream Server</h1>
        <p>This is a test upstream server for the WAF project. Use the endpoints below to test WAF functionality.</p>
        
        <h2>Available Endpoints:</h2>
        
        <div class="endpoint">
            <div class="method">GET</div>
            <strong>/</strong> - This page
        </div>
        
        <div class="endpoint">
            <div class="method">GET</div>
            <strong>/api/test</strong> - Safe test endpoint
            <br><small>Returns: JSON response with message and timestamp</small>
        </div>
        
        <div class="endpoint vulnerable">
            <div class="method">GET</div>
            <strong>/api/vulnerable</strong> - Vulnerable endpoint for testing
            <br><small>⚠️ This endpoint echoes query parameters (for WAF testing)</small>
            <br><small>Example: <code>/api/vulnerable?param=value</code></small>
        </div>
        
        <h2>WAF Test Cases:</h2>
        <p>Try these requests to test WAF protection:</p>
        <ul>
            <li><strong>SQL Injection:</strong> <code>/api/vulnerable?id=1' OR '1'='1</code></li>
            <li><strong>XSS:</strong> <code>/api/vulnerable?name=&lt;script&gt;alert('xss')&lt;/script&gt;</code></li>
            <li><strong>Path Traversal:</strong> <code>/api/vulnerable?file=../../../etc/passwd</code></li>
            <li><strong>Command Injection:</strong> <code>/api/vulnerable?cmd=; cat /etc/passwd</code></li>
        </ul>
        
        <p><em>Note: These are safe test cases. The upstream server doesn't actually execute any malicious code.</em></p>
    </div>
</body>
</html>
