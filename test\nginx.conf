events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        location / {
            try_files $uri $uri/ =404;
        }

        location /api/test {
            add_header Content-Type application/json;
            return 200 '{"message": "Hello from upstream server!", "timestamp": "$time_iso8601"}';
        }

        location /api/vulnerable {
            add_header Content-Type application/json;
            return 200 '{"message": "This is a vulnerable endpoint for testing", "query": "$args"}';
        }

        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
    }
}
